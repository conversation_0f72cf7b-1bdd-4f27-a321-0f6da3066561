using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using BCrypt.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Authentication;
using SmsGateway.Database;
using SmsGateway.Database.Entities;
using IAuthenticationService = SmsGateway.Core.Interfaces.IAuthenticationService;
using LoginRequest = SmsGateway.Core.Models.Authentication.LoginRequest;

namespace SmsGateway.Services;

/// <summary>
/// Authentication service implementation
/// </summary>
public class AuthenticationService : IAuthenticationService
{
    private readonly SmsGatewayDbContext _dbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthenticationService> _logger;

    public AuthenticationService(
        SmsGatewayDbContext dbContext,
        IConfiguration configuration,
        ILogger<AuthenticationService> logger)
    {
        _dbContext = dbContext;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<LoginResponse> LoginAsync(LoginRequest request, string? ipAddress = null, string? userAgent = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _dbContext.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Username == request.Username && u.IsActive, cancellationToken);

            if (user == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            {
                _logger.LogWarning("Failed login attempt for username: {Username} from IP: {IpAddress}", request.Username, ipAddress);
                return new LoginResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid username or password"
                };
            }

            // Update last login
            user.LastLoginAt = DateTime.UtcNow;
            user.LastLoginIp = ipAddress;
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Generate tokens
            var tokenId = Guid.NewGuid().ToString();
            var accessToken = GenerateAccessToken(user, tokenId);
            var refreshToken = GenerateRefreshToken();

            // Create user session
            var session = new UserSessionEntity
            {
                Id = Guid.NewGuid(),
                TenantId = user.TenantId,
                UserId = user.Id,
                TokenId = tokenId,
                RefreshTokenHash = BCrypt.Net.BCrypt.HashPassword(refreshToken),
                ExpiresAt = DateTime.UtcNow.AddDays(30), // Refresh token expires in 30 days
                IpAddress = ipAddress,
                UserAgent = userAgent,
                IsActive = true,
                StartedAt = DateTime.UtcNow
            };

            _dbContext.UserSessions.Add(session);
            await _dbContext.SaveChangesAsync(cancellationToken);

            return new LoginResponse
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresIn = 3600, // 1 hour
                TokenType = "Bearer",
                User = new UserInfo
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList()
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for username: {Username}", request.Username);
            return new LoginResponse
            {
                Success = false,
                ErrorMessage = "An error occurred during login"
            };
        }
    }

    public async Task<LoginResponse> RefreshTokenAsync(RefreshTokenRequest request, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find session by verifying refresh token hash
            var sessions = await _dbContext.UserSessions
                .Include(s => s.User)
                .ThenInclude(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .Where(s => s.IsActive && s.ExpiresAt > DateTime.UtcNow && s.RefreshTokenHash != null)
                .ToListAsync(cancellationToken);

            var session = sessions.FirstOrDefault(s =>
                BCrypt.Net.BCrypt.Verify(request.RefreshToken, s.RefreshTokenHash));

            if (session == null)
            {
                return new LoginResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid or expired refresh token"
                };
            }

            // Generate new tokens
            var tokenId = Guid.NewGuid().ToString();
            var accessToken = GenerateAccessToken(session.User, tokenId);
            var refreshToken = GenerateRefreshToken();

            // Update session
            session.TokenId = tokenId;
            session.RefreshTokenHash = BCrypt.Net.BCrypt.HashPassword(refreshToken);
            session.ExpiresAt = DateTime.UtcNow.AddDays(30);
            session.IpAddress = ipAddress;
            session.LastActivityAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync(cancellationToken);

            return new LoginResponse
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresIn = 3600,
                TokenType = "Bearer",
                User = new UserInfo
                {
                    Id = session.User.Id,
                    Username = session.User.Username,
                    Email = session.User.Email,
                    FirstName = session.User.FirstName,
                    LastName = session.User.LastName,
                    Roles = session.User.UserRoles.Select(ur => ur.Role.Name).ToList()
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return new LoginResponse
            {
                Success = false,
                ErrorMessage = "An error occurred during token refresh"
            };
        }
    }

    public async Task<bool> LogoutAsync(string tokenId, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _dbContext.UserSessions
                .FirstOrDefaultAsync(s => s.TokenId == tokenId && s.IsActive, cancellationToken);

            if (session != null)
            {
                session.IsActive = false;
                session.LastActivityAt = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync(cancellationToken);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout for token: {TokenId}", tokenId);
            return false;
        }
    }

    public async Task<UserInfo?> GetUserByIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var user = await _dbContext.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

        if (user == null)
            return null;

        return new UserInfo
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList()
        };
    }

    public async Task<bool> HasPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default)
    {
        // Simple role-based permission check
        var user = await _dbContext.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

        if (user == null)
            return false;

        // Admin role has all permissions
        if (user.UserRoles.Any(ur => ur.Role.Name == "Admin"))
            return true;

        // TODO: Implement more granular permission system
        return true;
    }

    public async Task<List<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var user = await _dbContext.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

        if (user == null)
            return new List<string>();

        // TODO: Implement proper permission system
        var permissions = new List<string>();
        
        foreach (var userRole in user.UserRoles)
        {
            switch (userRole.Role.Name)
            {
                case "Admin":
                    permissions.AddRange(new[] { "sms.send", "sms.view", "templates.manage", "providers.manage", "users.manage" });
                    break;
                case "User":
                    permissions.AddRange(new[] { "sms.send", "sms.view", "templates.view" });
                    break;
            }
        }

        return permissions.Distinct().ToList();
    }

    private string GenerateAccessToken(UserEntity user, string tokenId)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "your-secret-key-here"));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
            new Claim(JwtRegisteredClaimNames.Jti, tokenId),
            new Claim(ClaimTypes.Name, user.Username),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim("tenant_id", user.TenantId)
        };

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"] ?? "SmsGateway",
            audience: _configuration["Jwt:Audience"] ?? "SmsGateway",
            claims: claims,
            expires: DateTime.UtcNow.AddHours(1),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    private string GenerateRefreshToken()
    {
        return Guid.NewGuid().ToString();
    }

    public async Task<ApiKeyValidationResult> ValidateApiKeyAsync(string apiKey, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find API key by hashing the provided key and comparing
            var apiKeys = await _dbContext.ApiKeys
                .Where(ak => ak.IsActive && ak.ExpiresAt > DateTime.UtcNow)
                .ToListAsync(cancellationToken);

            var apiKeyEntity = apiKeys.FirstOrDefault(ak =>
                BCrypt.Net.BCrypt.Verify(apiKey, ak.KeyHash));

            if (apiKeyEntity == null)
            {
                return new ApiKeyValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Invalid API key"
                };
            }

            // Check IP restrictions
            if (apiKeyEntity.AllowedIpAddresses?.Any() == true &&
                !string.IsNullOrEmpty(ipAddress) &&
                !apiKeyEntity.AllowedIpAddresses.Contains(ipAddress))
            {
                return new ApiKeyValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "IP address not allowed"
                };
            }

            // Update usage
            apiKeyEntity.LastUsedAt = DateTime.UtcNow;
            apiKeyEntity.UsageCount++;
            await _dbContext.SaveChangesAsync(cancellationToken);

            return new ApiKeyValidationResult
            {
                IsValid = true,
                ApiKeyId = apiKeyEntity.Id,
                TenantId = apiKeyEntity.TenantId,
                Permissions = apiKeyEntity.Permissions ?? new List<string>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating API key");
            return new ApiKeyValidationResult
            {
                IsValid = false,
                ErrorMessage = "Internal error"
            };
        }
    }

    public async Task UpdateApiKeyUsageAsync(Guid apiKeyId, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var apiKey = await _dbContext.ApiKeys
                .FirstOrDefaultAsync(ak => ak.Id == apiKeyId, cancellationToken);

            if (apiKey != null)
            {
                apiKey.LastUsedAt = DateTime.UtcNow;
                apiKey.UsageCount++;
                await _dbContext.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating API key usage for {ApiKeyId}", apiKeyId);
        }
    }

    public async Task<UserInfo> RegisterAsync(RegisterRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user already exists
            var existingUser = await _dbContext.Users
                .FirstOrDefaultAsync(u => u.Username == request.Username || u.Email == request.Email, cancellationToken);

            if (existingUser != null)
            {
                throw new InvalidOperationException("User with this username or email already exists");
            }

            var user = new UserEntity
            {
                Id = Guid.NewGuid(),
                TenantId = GetCurrentTenantId(),
                Username = request.Username,
                Email = request.Email,
                FirstName = request.FirstName,
                LastName = request.LastName,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                IsActive = true,
                EmailVerified = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.Users.Add(user);
            await _dbContext.SaveChangesAsync(cancellationToken);

            return new UserInfo
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Roles = new List<string>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering user: {Username}", request.Username);
            throw;
        }
    }

    public async Task<bool> ChangePasswordAsync(Guid userId, ChangePasswordRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _dbContext.Users
                .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

            if (user == null)
                return false;

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.PasswordHash))
                return false;

            // Update password
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user: {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> RequestPasswordResetAsync(ResetPasswordRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _dbContext.Users
                .FirstOrDefaultAsync(u => u.Email == request.Email && u.IsActive, cancellationToken);

            if (user == null)
                return true; // Don't reveal if email exists

            // TODO: Generate reset token and send email
            _logger.LogInformation("Password reset requested for user: {Email}", request.Email);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting password reset for: {Email}", request.Email);
            return false;
        }
    }

    public async Task<bool> ConfirmPasswordResetAsync(ConfirmPasswordResetRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Validate reset token and update password
            _logger.LogInformation("Password reset confirmation attempted with token: {Token}", request.Token);
            return false; // Not implemented yet
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming password reset");
            return false;
        }
    }

    public async Task<bool> VerifyEmailAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Validate email verification token
            _logger.LogInformation("Email verification attempted with token: {Token}", token);
            return false; // Not implemented yet
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying email");
            return false;
        }
    }

    public async Task<UserInfo?> GetUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await GetUserByIdAsync(userId, cancellationToken);
    }

    public async Task<UserInfo?> GetUserByUsernameAsync(string username, string tenantId, CancellationToken cancellationToken = default)
    {
        var user = await _dbContext.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Username == username && u.TenantId == tenantId && u.IsActive, cancellationToken);

        if (user == null)
            return null;

        return new UserInfo
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList()
        };
    }

    public async Task<TokenValidationResult> ValidateTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "your-secret-key-here");

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["Jwt:Issuer"] ?? "SmsGateway",
                ValidateAudience = true,
                ValidAudience = _configuration["Jwt:Audience"] ?? "SmsGateway",
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);

            var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var tenantIdClaim = principal.FindFirst("tenant_id")?.Value;
            var tokenIdClaim = principal.FindFirst(JwtRegisteredClaimNames.Jti)?.Value;

            if (Guid.TryParse(userIdClaim, out var userId))
            {
                var permissions = await GetUserPermissionsAsync(userId, cancellationToken);

                return new TokenValidationResult
                {
                    IsValid = true,
                    UserId = userId,
                    TenantId = tenantIdClaim,
                    TokenId = tokenIdClaim,
                    Permissions = permissions,
                    ExpiresAt = validatedToken.ValidTo
                };
            }

            return new TokenValidationResult
            {
                IsValid = false,
                ErrorMessage = "Invalid user ID in token"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token");
            return new TokenValidationResult
            {
                IsValid = false,
                ErrorMessage = ex.Message
            };
        }
    }
}
