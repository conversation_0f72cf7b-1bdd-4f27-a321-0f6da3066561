using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using BCrypt.Net;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Authentication;
using SmsGateway.Database;
using SmsGateway.Database.Entities;
using Microsoft.EntityFrameworkCore;

namespace SmsGateway.Services;

/// <summary>
/// Authentication service implementation
/// </summary>
public class AuthenticationService : IAuthenticationService
{
    private readonly SmsGatewayDbContext _dbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthenticationService> _logger;

    public AuthenticationService(
        SmsGatewayDbContext dbContext,
        IConfiguration configuration,
        ILogger<AuthenticationService> logger)
    {
        _dbContext = dbContext;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<LoginResponse> LoginAsync(LoginRequest request, string? ipAddress = null, string? userAgent = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _dbContext.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Username == request.Username && u.IsActive, cancellationToken);

            if (user == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            {
                _logger.LogWarning("Failed login attempt for username: {Username} from IP: {IpAddress}", request.Username, ipAddress);
                return new LoginResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid username or password"
                };
            }

            // Update last login
            user.LastLoginAt = DateTime.UtcNow;
            user.LastLoginIp = ipAddress;
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Generate tokens
            var tokenId = Guid.NewGuid().ToString();
            var accessToken = GenerateAccessToken(user, tokenId);
            var refreshToken = GenerateRefreshToken();

            // Create user session
            var session = new UserSessionEntity
            {
                Id = Guid.NewGuid(),
                TenantId = user.TenantId,
                UserId = user.Id,
                TokenId = tokenId,
                RefreshToken = refreshToken,
                ExpiresAt = DateTime.UtcNow.AddDays(30), // Refresh token expires in 30 days
                IpAddress = ipAddress,
                UserAgent = userAgent,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _dbContext.UserSessions.Add(session);
            await _dbContext.SaveChangesAsync(cancellationToken);

            return new LoginResponse
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresIn = 3600, // 1 hour
                TokenType = "Bearer",
                User = new UserInfo
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList()
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for username: {Username}", request.Username);
            return new LoginResponse
            {
                Success = false,
                ErrorMessage = "An error occurred during login"
            };
        }
    }

    public async Task<LoginResponse> RefreshTokenAsync(RefreshTokenRequest request, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _dbContext.UserSessions
                .Include(s => s.User)
                .ThenInclude(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(s => s.RefreshToken == request.RefreshToken && s.IsActive && s.ExpiresAt > DateTime.UtcNow, cancellationToken);

            if (session == null)
            {
                return new LoginResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid or expired refresh token"
                };
            }

            // Generate new tokens
            var tokenId = Guid.NewGuid().ToString();
            var accessToken = GenerateAccessToken(session.User, tokenId);
            var refreshToken = GenerateRefreshToken();

            // Update session
            session.TokenId = tokenId;
            session.RefreshToken = refreshToken;
            session.ExpiresAt = DateTime.UtcNow.AddDays(30);
            session.IpAddress = ipAddress;
            session.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync(cancellationToken);

            return new LoginResponse
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresIn = 3600,
                TokenType = "Bearer",
                User = new UserInfo
                {
                    Id = session.User.Id,
                    Username = session.User.Username,
                    Email = session.User.Email,
                    FirstName = session.User.FirstName,
                    LastName = session.User.LastName,
                    Roles = session.User.UserRoles.Select(ur => ur.Role.Name).ToList()
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return new LoginResponse
            {
                Success = false,
                ErrorMessage = "An error occurred during token refresh"
            };
        }
    }

    public async Task<bool> LogoutAsync(string tokenId, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _dbContext.UserSessions
                .FirstOrDefaultAsync(s => s.TokenId == tokenId && s.IsActive, cancellationToken);

            if (session != null)
            {
                session.IsActive = false;
                session.UpdatedAt = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync(cancellationToken);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout for token: {TokenId}", tokenId);
            return false;
        }
    }

    public async Task<UserInfo?> GetUserByIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var user = await _dbContext.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

        if (user == null)
            return null;

        return new UserInfo
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList()
        };
    }

    public async Task<bool> HasPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default)
    {
        // Simple role-based permission check
        var user = await _dbContext.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

        if (user == null)
            return false;

        // Admin role has all permissions
        if (user.UserRoles.Any(ur => ur.Role.Name == "Admin"))
            return true;

        // TODO: Implement more granular permission system
        return true;
    }

    public async Task<List<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var user = await _dbContext.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

        if (user == null)
            return new List<string>();

        // TODO: Implement proper permission system
        var permissions = new List<string>();
        
        foreach (var userRole in user.UserRoles)
        {
            switch (userRole.Role.Name)
            {
                case "Admin":
                    permissions.AddRange(new[] { "sms.send", "sms.view", "templates.manage", "providers.manage", "users.manage" });
                    break;
                case "User":
                    permissions.AddRange(new[] { "sms.send", "sms.view", "templates.view" });
                    break;
            }
        }

        return permissions.Distinct().ToList();
    }

    private string GenerateAccessToken(UserEntity user, string tokenId)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "your-secret-key-here"));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
            new Claim(JwtRegisteredClaimNames.Jti, tokenId),
            new Claim(ClaimTypes.Name, user.Username),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim("tenant_id", user.TenantId)
        };

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"] ?? "SmsGateway",
            audience: _configuration["Jwt:Audience"] ?? "SmsGateway",
            claims: claims,
            expires: DateTime.UtcNow.AddHours(1),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    private string GenerateRefreshToken()
    {
        return Guid.NewGuid().ToString();
    }

    public Task<ApiKeyValidationResult> ValidateApiKeyAsync(string apiKey, string? ipAddress = null, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}
