<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <IsServiceProject>true</IsServiceProject>
        <AssemblyTitle>SMS Gateway Services</AssemblyTitle>
        <AssemblyDescription>Business logic services for SMS Gateway</AssemblyDescription>
        <LangVersion>default</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Hangfire.Core" Version="$(HangfireVersion)" />
        <PackageReference Include="StackExchange.Redis" Version="$(StackExchangeRedisVersion)" />
        <PackageReference Include="DotLiquid" Version="$(DotLiquidVersion)" />
        <PackageReference Include="BCrypt.Net-Next" Version="$(BCryptVersion)" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="$(JwtVersion)" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="$(JwtVersion)" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\SmsGateway.Core\SmsGateway.Core.csproj" />
        <ProjectReference Include="..\SmsGateway.Database\SmsGateway.Database.csproj" />
    </ItemGroup>

</Project>
