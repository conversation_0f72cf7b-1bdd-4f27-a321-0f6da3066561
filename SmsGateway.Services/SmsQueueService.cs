using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Hangfire;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Core.Models.Common;
using SmsGateway.Database;
using SmsGateway.Database.Entities;

namespace SmsGateway.Services;

/// <summary>
/// SMS queue service implementation for background processing
/// </summary>
public class SmsQueueService : ISmsQueueService
{
    private readonly SmsGatewayDbContext _dbContext;
    private readonly ISmsService _smsService;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly ILogger<SmsQueueService> _logger;

    public SmsQueueService(
        SmsGatewayDbContext dbContext,
        ISmsService smsService,
        IBackgroundJobClient backgroundJobClient,
        ILogger<SmsQueueService> logger)
    {
        _dbContext = dbContext;
        _smsService = smsService;
        _backgroundJobClient = backgroundJobClient;
        _logger = logger;
    }

    public async Task<Guid> QueueSmsAsync(SmsRequest request, string tenantId, int priority = 100, DateTime? processAt = null, CancellationToken cancellationToken = default)
    {
        var queueItem = new SmsQueueEntity
        {
            Id = Guid.NewGuid(),
            TenantId = tenantId,
            Status = QueueStatus.Pending,
            Priority = priority,
            ProcessAt = processAt ?? DateTime.UtcNow,
            RequestData = System.Text.Json.JsonSerializer.Serialize(request),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _dbContext.SmsQueue.Add(queueItem);
        await _dbContext.SaveChangesAsync(cancellationToken);

        // Schedule background job
        string jobId;
        if (processAt.HasValue && processAt.Value > DateTime.UtcNow)
        {
            jobId = _backgroundJobClient.Schedule(() => ProcessQueuedSmsAsync(queueItem.Id, CancellationToken.None), processAt.Value);
        }
        else
        {
            jobId = _backgroundJobClient.Enqueue(() => ProcessQueuedSmsAsync(queueItem.Id, CancellationToken.None));
        }

        // Update queue item with job ID
        queueItem.HangfireJobId = jobId;
        await _dbContext.SaveChangesAsync(cancellationToken);

        return queueItem.Id;
    }

    public async Task<IEnumerable<Guid>> QueueBatchSmsAsync(IEnumerable<SmsRequest> requests, string tenantId, int priority = 100, DateTime? processAt = null, CancellationToken cancellationToken = default)
    {
        var queueIds = new List<Guid>();

        foreach (var request in requests)
        {
            var queueId = await QueueSmsAsync(request, tenantId, priority, processAt, cancellationToken);
            queueIds.Add(queueId);
        }

        return queueIds;
    }

    public async Task<QueueProcessingResult> ProcessQueuedSmsAsync(Guid queueId, CancellationToken cancellationToken = default)
    {
        try
        {
            var queueItem = await _dbContext.SmsQueue
                .FirstOrDefaultAsync(q => q.Id == queueId, cancellationToken);

            if (queueItem == null)
            {
                return new QueueProcessingResult
                {
                    Success = false,
                    ErrorMessage = "Queue item not found"
                };
            }

            if (queueItem.Status != QueueStatus.Pending)
            {
                return new QueueProcessingResult
                {
                    Success = false,
                    ErrorMessage = $"Queue item is not in pending status. Current status: {queueItem.Status}"
                };
            }

            // Update status to processing
            queueItem.Status = QueueStatus.Processing;
            queueItem.ProcessedAt = DateTime.UtcNow;
            queueItem.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Deserialize request
            var request = System.Text.Json.JsonSerializer.Deserialize<SmsRequest>(queueItem.RequestData);
            if (request == null)
            {
                queueItem.Status = QueueStatus.Failed;
                queueItem.ErrorMessage = "Failed to deserialize request data";
                queueItem.UpdatedAt = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync(cancellationToken);

                return new QueueProcessingResult
                {
                    Success = false,
                    ErrorMessage = "Failed to deserialize request data"
                };
            }

            // Process SMS
            var response = await _smsService.SendSmsAsync(request, cancellationToken);

            // Update queue item with result
            if (response.Success)
            {
                queueItem.Status = QueueStatus.Completed;
                queueItem.SmsMessageId = Guid.TryParse(response.MessageId, out var messageId) ? messageId : null;
            }
            else
            {
                queueItem.Status = QueueStatus.Failed;
                queueItem.ErrorMessage = response.ErrorMessage;
            }

            queueItem.CompletedAt = DateTime.UtcNow;
            queueItem.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync(cancellationToken);

            return new QueueProcessingResult
            {
                Success = response.Success,
                MessageId = response.MessageId,
                ErrorMessage = response.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process queued SMS with ID: {QueueId}", queueId);

            // Update queue item with error
            var queueItem = await _dbContext.SmsQueue
                .FirstOrDefaultAsync(q => q.Id == queueId, cancellationToken);

            if (queueItem != null)
            {
                queueItem.Status = QueueStatus.Failed;
                queueItem.ErrorMessage = ex.Message;
                queueItem.UpdatedAt = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync(cancellationToken);
            }

            return new QueueProcessingResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<QueueStatusResponse?> GetQueueStatusAsync(Guid queueId, string tenantId, CancellationToken cancellationToken = default)
    {
        var queueItem = await _dbContext.SmsQueue
            .FirstOrDefaultAsync(q => q.Id == queueId && q.TenantId == tenantId, cancellationToken);

        if (queueItem == null)
            return null;

        return new QueueStatusResponse
        {
            QueueId = queueItem.Id,
            Status = queueItem.Status,
            Priority = queueItem.Priority,
            ProcessAt = queueItem.ProcessAt,
            ProcessedAt = queueItem.ProcessedAt,
            CompletedAt = queueItem.CompletedAt,
            ErrorMessage = queueItem.ErrorMessage,
            SmsMessageId = queueItem.SmsMessageId,
            CreatedAt = queueItem.CreatedAt
        };
    }

    public async Task<bool> CancelQueuedSmsAsync(Guid queueId, string tenantId, CancellationToken cancellationToken = default)
    {
        var queueItem = await _dbContext.SmsQueue
            .FirstOrDefaultAsync(q => q.Id == queueId && q.TenantId == tenantId, cancellationToken);

        if (queueItem == null || queueItem.Status != QueueStatus.Pending)
            return false;

        // Cancel Hangfire job if exists
        if (!string.IsNullOrEmpty(queueItem.HangfireJobId))
        {
            _backgroundJobClient.Delete(queueItem.HangfireJobId);
        }

        // Update status
        queueItem.Status = QueueStatus.Cancelled;
        queueItem.UpdatedAt = DateTime.UtcNow;
        await _dbContext.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<bool> RetryFailedSmsAsync(Guid queueId, string tenantId, CancellationToken cancellationToken = default)
    {
        var queueItem = await _dbContext.SmsQueue
            .FirstOrDefaultAsync(q => q.Id == queueId && q.TenantId == tenantId, cancellationToken);

        if (queueItem == null || queueItem.Status != QueueStatus.Failed)
            return false;

        // Reset status and schedule for immediate processing
        queueItem.Status = QueueStatus.Pending;
        queueItem.ProcessAt = DateTime.UtcNow;
        queueItem.ProcessedAt = null;
        queueItem.CompletedAt = null;
        queueItem.ErrorMessage = null;
        queueItem.UpdatedAt = DateTime.UtcNow;

        // Schedule new background job
        var jobId = _backgroundJobClient.Enqueue(() => ProcessQueuedSmsAsync(queueItem.Id, CancellationToken.None));
        queueItem.HangfireJobId = jobId;

        await _dbContext.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<IEnumerable<SmsQueueEntity>> GetPendingQueueItemsAsync(int maxItems = 100, CancellationToken cancellationToken = default)
    {
        return await _dbContext.SmsQueue
            .Where(q => q.Status == QueueStatus.Pending && q.ProcessAt <= DateTime.UtcNow)
            .OrderBy(q => q.Priority)
            .ThenBy(q => q.CreatedAt)
            .Take(maxItems)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> CleanupOldQueueItemsAsync(DateTime olderThan, CancellationToken cancellationToken = default)
    {
        var itemsToDelete = await _dbContext.SmsQueue
            .Where(q => (q.Status == QueueStatus.Completed || q.Status == QueueStatus.Failed || q.Status == QueueStatus.Cancelled) 
                       && q.UpdatedAt < olderThan)
            .ToListAsync(cancellationToken);

        _dbContext.SmsQueue.RemoveRange(itemsToDelete);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return itemsToDelete.Count;
    }
}

/// <summary>
/// Queue processing result
/// </summary>
public class QueueProcessingResult
{
    public bool Success { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Queue status response
/// </summary>
public class QueueStatusResponse
{
    public Guid QueueId { get; set; }
    public QueueStatus Status { get; set; }
    public int Priority { get; set; }
    public DateTime ProcessAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? SmsMessageId { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Queue status enumeration
/// </summary>
public enum QueueStatus
{
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}
