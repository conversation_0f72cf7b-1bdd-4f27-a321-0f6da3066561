using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Common;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Database;
using SmsGateway.Database.Entities;

namespace SmsGateway.Services;

/// <summary>
/// Main SMS service implementation for business logic
/// </summary>
public class SmsService : ISmsService
{
    private readonly IPluginManager _pluginManager;
    private readonly SmsGatewayDbContext _dbContext;
    private readonly ILogger<SmsService> _logger;

    public SmsService(
        IPluginManager pluginManager,
        SmsGatewayDbContext dbContext,
        ILogger<SmsService> logger)
    {
        _pluginManager = pluginManager;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<SmsResponse> SendSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get the best provider for this request
            var provider = _pluginManager.GetBestProvider(request);
            if (provider == null)
            {
                return new SmsResponse
                {
                    Success = false,
                    Status = SmsStatus.Failed,
                    ErrorMessage = "No suitable SMS provider available",
                    ErrorCode = "NO_PROVIDER"
                };
            }

            // Create SMS message entity
            var smsMessage = new SmsMessageEntity
            {
                Id = Guid.NewGuid(),
                TenantId = GetCurrentTenantId(),
                To = request.To,
                From = request.From,
                OriginalMessage = request.Message,
                ProcessedMessage = request.Message, // TODO: Apply template processing
                Provider = provider.Name,
                Status = SmsStatus.Pending,
                Priority = request.Priority,
                ScheduledAt = request.ScheduledAt,
                Reference = request.Reference,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Save to database
            _dbContext.SmsMessages.Add(smsMessage);
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Send via provider
            var response = await provider.SendSmsAsync(request, cancellationToken);

            // Update message with response
            smsMessage.ExternalMessageId = response.MessageId;
            smsMessage.Status = response.Status;
            smsMessage.Cost = response.Cost;
            smsMessage.Currency = response.Currency;
            smsMessage.Segments = response.Segments;
            smsMessage.ErrorCode = response.ErrorCode;
            smsMessage.ErrorMessage = response.ErrorMessage;
            smsMessage.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync(cancellationToken);

            // Add to response
            response.MessageId = smsMessage.Id.ToString();
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS to {To}", request.To);
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<BulkSmsResponse> SendBulkSmsAsync(SendBulkSmsRequest request, CancellationToken cancellationToken = default)
    {
        var responses = new List<SmsResponse>();
        var successCount = 0;

        foreach (var message in request.Messages)
        {
            var smsRequest = new SendSmsRequest
            {
                To = message.To,
                From = message.From ?? request.DefaultFrom,
                Message = message.Message,
                Provider = request.Provider,
                Priority = message.Priority,
                ScheduledAt = message.ScheduledAt,
                Reference = message.Reference
            };

            var response = await SendSmsAsync(smsRequest, cancellationToken);
            responses.Add(response);

            if (response.Success)
                successCount++;
        }

        return new BulkSmsResponse
        {
            Success = successCount > 0,
            TotalMessages = responses.Count,
            SuccessfulMessages = successCount,
            FailedMessages = responses.Count - successCount,
            MessageResponses = responses,
            TotalCost = responses.Where(r => r.Cost.HasValue).Sum(r => r.Cost.Value),
            Currency = responses.FirstOrDefault(r => !string.IsNullOrEmpty(r.Currency))?.Currency ?? "USD",
            Timestamp = DateTime.UtcNow
        };
    }

    public async Task<SmsResponse> ScheduleSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default)
    {
        if (!request.ScheduledAt.HasValue)
        {
            request.ScheduledAt = DateTime.UtcNow.AddMinutes(5); // Default to 5 minutes from now
        }

        return await SendSmsAsync(request, cancellationToken);
    }

    public async Task<SmsStatus> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (Guid.TryParse(messageId, out var id))
        {
            var message = await _dbContext.SmsMessages
                .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
            
            if (message != null)
            {
                return message.Status;
            }
        }

        return SmsStatus.Unknown;
    }

    public async Task<List<SmsMessageEntity>> GetMessageHistoryAsync(int page = 1, int pageSize = 50, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        
        return await _dbContext.SmsMessages
            .Where(m => m.TenantId == tenantId)
            .OrderByDescending(m => m.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<SmsStatistics> GetStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        var query = _dbContext.SmsMessages.Where(m => m.TenantId == tenantId);

        if (fromDate.HasValue)
            query = query.Where(m => m.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(m => m.CreatedAt <= toDate.Value);

        var total = await query.CountAsync(cancellationToken);
        var sent = await query.CountAsync(m => m.Status == SmsStatus.Sent, cancellationToken);
        var failed = await query.CountAsync(m => m.Status == SmsStatus.Failed, cancellationToken);
        var pending = await query.CountAsync(m => m.Status == SmsStatus.Pending, cancellationToken);

        return new SmsStatistics
        {
            TotalMessages = total,
            SentMessages = sent,
            FailedMessages = failed,
            PendingMessages = pending,
            TotalCost = await query.Where(m => m.Cost.HasValue).SumAsync(m => m.Cost!.Value, cancellationToken),
            FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30),
            ToDate = toDate ?? DateTime.UtcNow
        };
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId, string provider, CancellationToken cancellationToken = default)
    {
        if (Guid.TryParse(messageId, out var id))
        {
            var message = await _dbContext.SmsMessages
                .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);

            if (message != null)
            {
                return new MessageStatusResponse
                {
                    MessageId = messageId,
                    Status = message.Status,
                    Provider = message.Provider,
                    DeliveredAt = message.DeliveredAt,
                    ErrorMessage = message.ErrorMessage,
                    ErrorCode = message.ErrorCode
                };
            }
        }

        return new MessageStatusResponse
        {
            MessageId = messageId,
            Status = SmsStatus.Unknown,
            Provider = provider,
            ErrorMessage = "Message not found"
        };
    }

    public async Task<SmsHistoryResponse> GetSmsHistoryAsync(GetSmsHistoryRequest request, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        var query = _dbContext.SmsMessages.Where(m => m.TenantId == tenantId);

        if (request.FromDate.HasValue)
            query = query.Where(m => m.CreatedAt >= request.FromDate.Value);

        if (request.ToDate.HasValue)
            query = query.Where(m => m.CreatedAt <= request.ToDate.Value);

        if (!string.IsNullOrEmpty(request.Provider))
            query = query.Where(m => m.Provider == request.Provider);

        if (request.Status.HasValue)
            query = query.Where(m => m.Status == request.Status.Value);

        var total = await query.CountAsync(cancellationToken);
        var messages = await query
            .OrderByDescending(m => m.CreatedAt)
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        return new SmsHistoryResponse
        {
            Messages = messages.Select(m => new SmsHistoryItem
            {
                MessageId = m.Id.ToString(),
                To = m.To,
                From = m.From,
                Message = m.OriginalMessage,
                Status = m.Status,
                Provider = m.Provider,
                Cost = m.Cost,
                Currency = m.Currency,
                CreatedAt = m.CreatedAt,
                DeliveredAt = m.DeliveredAt,
                ErrorMessage = m.ErrorMessage
            }).ToList(),
            TotalCount = total,
            Page = request.Page,
            PageSize = request.PageSize,
            TotalPages = (int)Math.Ceiling((double)total / request.PageSize)
        };
    }

    public async Task<CostEstimateResponse> GetEstimatedCostAsync(GetCostEstimateRequest request, CancellationToken cancellationToken = default)
    {
        // Simple cost estimation - in real implementation, this would query provider APIs
        var estimatedCost = request.MessageCount * 0.05m; // $0.05 per message

        return new CostEstimateResponse
        {
            EstimatedCost = estimatedCost,
            Currency = "USD",
            MessageCount = request.MessageCount,
            Provider = request.Provider ?? "default",
            Timestamp = DateTime.UtcNow
        };
    }

    public async Task<MessageValidationResponse> ValidateMessageAsync(string message, MessageType messageType = MessageType.Text, CancellationToken cancellationToken = default)
    {
        var response = new MessageValidationResponse
        {
            IsValid = true,
            MessageType = messageType,
            OriginalMessage = message
        };

        // Basic validation
        if (string.IsNullOrWhiteSpace(message))
        {
            response.IsValid = false;
            response.Errors.Add("Message cannot be empty");
            return response;
        }

        // SMS length validation
        if (messageType == MessageType.Text)
        {
            response.Length = message.Length;
            response.Segments = CalculateSegments(message);

            if (message.Length > 1600) // Max for concatenated SMS
            {
                response.IsValid = false;
                response.Errors.Add("Message too long (max 1600 characters)");
            }
            else if (message.Length > 160)
            {
                response.Warnings.Add($"Message will be sent as {response.Segments} segments");
            }
        }

        return response;
    }

    private int CalculateSegments(string message)
    {
        if (message.Length <= 160) return 1;
        return (int)Math.Ceiling((double)message.Length / 153); // 153 chars per segment for concatenated SMS
    }

    public async Task<bool> CancelScheduledSmsAsync(string messageId, string provider, CancellationToken cancellationToken = default)
    {
        if (Guid.TryParse(messageId, out var id))
        {
            var message = await _dbContext.SmsMessages
                .FirstOrDefaultAsync(m => m.Id == id && m.Provider == provider, cancellationToken);

            if (message != null && message.Status == SmsStatus.Scheduled)
            {
                message.Status = SmsStatus.Cancelled;
                message.UpdatedAt = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync(cancellationToken);
                return true;
            }
        }
        return false;
    }

    public async Task<MessageDetailsResponse> GetMessageDetailsAsync(string messageId, string provider, CancellationToken cancellationToken = default)
    {
        if (Guid.TryParse(messageId, out var id))
        {
            var message = await _dbContext.SmsMessages
                .FirstOrDefaultAsync(m => m.Id == id && m.Provider == provider, cancellationToken);

            if (message != null)
            {
                return new MessageDetailsResponse
                {
                    MessageId = messageId,
                    To = message.To,
                    From = message.From,
                    Message = message.OriginalMessage,
                    Status = message.Status,
                    Provider = message.Provider,
                    Cost = message.Cost,
                    Currency = message.Currency,
                    CreatedAt = message.CreatedAt,
                    SentAt = message.SentAt,
                    DeliveredAt = message.DeliveredAt,
                    ErrorMessage = message.ErrorMessage,
                    ErrorCode = message.ErrorCode,
                    Segments = message.Segments,
                    Direction = MessageDirection.Outbound
                };
            }
        }

        return new MessageDetailsResponse
        {
            MessageId = messageId,
            Status = SmsStatus.Unknown,
            Provider = provider,
            ErrorMessage = "Message not found"
        };
    }

    public async Task<DeliveryReportResponse> GetDeliveryReportAsync(string messageId, string provider, CancellationToken cancellationToken = default)
    {
        if (Guid.TryParse(messageId, out var id))
        {
            var message = await _dbContext.SmsMessages
                .FirstOrDefaultAsync(m => m.Id == id && m.Provider == provider, cancellationToken);

            if (message != null)
            {
                return new DeliveryReportResponse
                {
                    MessageId = messageId,
                    Status = message.Status,
                    DeliveredAt = message.DeliveredAt,
                    Provider = message.Provider,
                    ErrorCode = message.ErrorCode,
                    ErrorMessage = message.ErrorMessage,
                    Timestamp = DateTime.UtcNow
                };
            }
        }

        return new DeliveryReportResponse
        {
            MessageId = messageId,
            Status = SmsStatus.Unknown,
            Provider = provider,
            ErrorMessage = "Message not found",
            Timestamp = DateTime.UtcNow
        };
    }

    public async Task<List<MessageStatusResponse>> GetBulkMessageStatusAsync(List<string> messageIds, string provider, CancellationToken cancellationToken = default)
    {
        var responses = new List<MessageStatusResponse>();

        foreach (var messageId in messageIds)
        {
            var response = await GetMessageStatusAsync(messageId, provider, cancellationToken);
            responses.Add(response);
        }

        return responses;
    }

    public async Task<SmsResponse> SendTemplateSmsAsync(SendTemplateSmsRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Process template with variables
            var processedMessage = request.Message; // Placeholder - should process template

            var smsRequest = new SendSmsRequest
            {
                To = request.To,
                From = request.From,
                Message = processedMessage,
                Provider = request.Provider,
                Priority = request.Priority,
                ScheduledAt = request.ScheduledAt,
                Reference = request.Reference
            };

            return await SendSmsAsync(smsRequest, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending template SMS to {To}", request.To);
            return new SmsResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                Provider = request.Provider ?? "unknown"
            };
        }
    }

    public async Task<PhoneValidationResponse> ValidatePhoneNumberAsync(string phoneNumber, string? countryCode = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // Basic phone number validation
            var response = new PhoneValidationResponse
            {
                PhoneNumber = phoneNumber,
                IsValid = true,
                CountryCode = countryCode,
                Timestamp = DateTime.UtcNow
            };

            // Simple validation - in real implementation, use a phone validation service
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                response.IsValid = false;
                response.Errors.Add("Phone number cannot be empty");
                return response;
            }

            // Remove common formatting
            var cleanNumber = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            if (cleanNumber.Length < 10 || cleanNumber.Length > 15)
            {
                response.IsValid = false;
                response.Errors.Add("Phone number must be between 10 and 15 digits");
            }

            if (!cleanNumber.All(char.IsDigit) && !cleanNumber.StartsWith("+"))
            {
                response.IsValid = false;
                response.Errors.Add("Phone number contains invalid characters");
            }

            response.FormattedNumber = cleanNumber;
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating phone number: {PhoneNumber}", phoneNumber);
            return new PhoneValidationResponse
            {
                PhoneNumber = phoneNumber,
                IsValid = false,
                Errors = new List<string> { "Validation error occurred" },
                Timestamp = DateTime.UtcNow
            };
        }
    }

    private string GetCurrentTenantId()
    {
        // TODO: Get from current context/claims
        return "default";
    }
}

/// <summary>
/// SMS statistics model
/// </summary>
public class SmsStatistics
{
    public int TotalMessages { get; set; }
    public int SentMessages { get; set; }
    public int FailedMessages { get; set; }
    public int PendingMessages { get; set; }
    public decimal TotalCost { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}
