using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Core.Models.Common;
using SmsGateway.Database;
using SmsGateway.Database.Entities;
using Microsoft.EntityFrameworkCore;

namespace SmsGateway.Services;

/// <summary>
/// Main SMS service implementation for business logic
/// </summary>
public class SmsService : ISmsService
{
    private readonly IPluginManager _pluginManager;
    private readonly SmsGatewayDbContext _dbContext;
    private readonly ILogger<SmsService> _logger;

    public SmsService(
        IPluginManager pluginManager,
        SmsGatewayDbContext dbContext,
        ILogger<SmsService> logger)
    {
        _pluginManager = pluginManager;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<SmsResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get the best provider for this request
            var provider = _pluginManager.GetBestProvider(request);
            if (provider == null)
            {
                return new SmsResponse
                {
                    Success = false,
                    Status = SmsStatus.Failed,
                    ErrorMessage = "No suitable SMS provider available",
                    ErrorCode = "NO_PROVIDER"
                };
            }

            // Create SMS message entity
            var smsMessage = new SmsMessageEntity
            {
                Id = Guid.NewGuid(),
                TenantId = GetCurrentTenantId(),
                To = request.To,
                From = request.From,
                OriginalMessage = request.Message,
                ProcessedMessage = request.Message, // TODO: Apply template processing
                Provider = provider.Name,
                Status = SmsStatus.Pending,
                Priority = request.Priority,
                ScheduledAt = request.ScheduledAt,
                Reference = request.Reference,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Save to database
            _dbContext.SmsMessages.Add(smsMessage);
            await _dbContext.SaveChangesAsync(cancellationToken);

            // Send via provider
            var response = await provider.SendSmsAsync(request, cancellationToken);

            // Update message with response
            smsMessage.ExternalMessageId = response.MessageId;
            smsMessage.Status = response.Status;
            smsMessage.Cost = response.Cost;
            smsMessage.Currency = response.Currency;
            smsMessage.Segments = response.Segments;
            smsMessage.ErrorCode = response.ErrorCode;
            smsMessage.ErrorMessage = response.ErrorMessage;
            smsMessage.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync(cancellationToken);

            // Add to response
            response.MessageId = smsMessage.Id.ToString();
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS to {To}", request.To);
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<BulkSmsResponse> SendBulkSmsAsync(BulkSmsRequest request, CancellationToken cancellationToken = default)
    {
        var responses = new List<SmsResponse>();
        var successCount = 0;

        foreach (var message in request.Messages)
        {
            var smsRequest = new SmsRequest
            {
                To = message.To,
                From = message.From ?? request.DefaultFrom,
                Message = message.Message,
                Provider = request.Provider,
                Priority = message.Priority,
                ScheduledAt = message.ScheduledAt,
                Reference = message.Reference
            };

            var response = await SendSmsAsync(smsRequest, cancellationToken);
            responses.Add(response);

            if (response.Success)
                successCount++;
        }

        return new BulkSmsResponse
        {
            Success = successCount > 0,
            TotalMessages = responses.Count,
            SuccessfulMessages = successCount,
            FailedMessages = responses.Count - successCount,
            MessageResponses = responses,
            TotalCost = responses.Where(r => r.Cost.HasValue).Sum(r => r.Cost.Value),
            Currency = responses.FirstOrDefault(r => !string.IsNullOrEmpty(r.Currency))?.Currency ?? "USD",
            Timestamp = DateTime.UtcNow
        };
    }

    public async Task<SmsResponse> ScheduleSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        if (!request.ScheduledAt.HasValue)
        {
            request.ScheduledAt = DateTime.UtcNow.AddMinutes(5); // Default to 5 minutes from now
        }

        return await SendSmsAsync(request, cancellationToken);
    }

    public async Task<SmsStatus> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (Guid.TryParse(messageId, out var id))
        {
            var message = await _dbContext.SmsMessages
                .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
            
            if (message != null)
            {
                return message.Status;
            }
        }

        return SmsStatus.Unknown;
    }

    public async Task<List<SmsMessageEntity>> GetMessageHistoryAsync(int page = 1, int pageSize = 50, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        
        return await _dbContext.SmsMessages
            .Where(m => m.TenantId == tenantId)
            .OrderByDescending(m => m.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<SmsStatistics> GetStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        var query = _dbContext.SmsMessages.Where(m => m.TenantId == tenantId);

        if (fromDate.HasValue)
            query = query.Where(m => m.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(m => m.CreatedAt <= toDate.Value);

        var total = await query.CountAsync(cancellationToken);
        var sent = await query.CountAsync(m => m.Status == SmsStatus.Sent, cancellationToken);
        var failed = await query.CountAsync(m => m.Status == SmsStatus.Failed, cancellationToken);
        var pending = await query.CountAsync(m => m.Status == SmsStatus.Pending, cancellationToken);

        return new SmsStatistics
        {
            TotalMessages = total,
            SentMessages = sent,
            FailedMessages = failed,
            PendingMessages = pending,
            TotalCost = await query.Where(m => m.Cost.HasValue).SumAsync(m => m.Cost!.Value, cancellationToken),
            FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30),
            ToDate = toDate ?? DateTime.UtcNow
        };
    }

    private string GetCurrentTenantId()
    {
        // TODO: Get from current context/claims
        return "default";
    }
}

/// <summary>
/// SMS statistics model
/// </summary>
public class SmsStatistics
{
    public int TotalMessages { get; set; }
    public int SentMessages { get; set; }
    public int FailedMessages { get; set; }
    public int PendingMessages { get; set; }
    public decimal TotalCost { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}
