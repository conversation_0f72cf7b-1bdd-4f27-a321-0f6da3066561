using DotLiquid;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Core.Models.Common;
using SmsGateway.Core.Models.Database;
using SmsGateway.Core.Models.Templates;
using SmsGateway.Database;
using SmsGateway.Database.Entities;

namespace SmsGateway.Services;

/// <summary>
/// Template service implementation for SMS template management and processing
/// </summary>
public class TemplateService : ITemplateService
{
    private readonly SmsGatewayDbContext _dbContext;
    private readonly ILogger<TemplateService> _logger;

    public TemplateService(
        SmsGatewayDbContext dbContext,
        ILogger<TemplateService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<List<SmsTemplate>> GetTemplatesAsync(string tenantId, string? category = null, bool? isActive = null, CancellationToken cancellationToken = default)
    {
        var query = _dbContext.SmsTemplates.Where(t => t.TenantId == tenantId);

        if (!string.IsNullOrEmpty(category))
            query = query.Where(t => t.Category == category);

        if (isActive.HasValue)
            query = query.Where(t => t.IsActive == isActive.Value);

        var entities = await query
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);

        return entities.Select(ConvertToSmsTemplate).ToList();
    }



    private string GetCurrentTenantId()
    {
        // TODO: Get from current context/claims
        return "default";
    }

    private string GetCurrentUserId()
    {
        // TODO: Get from current context/claims
        return "system";
    }

    public async Task<SmsTemplate?> GetTemplateAsync(Guid id, string tenantId, CancellationToken cancellationToken = default)
    {
        var entity = await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Id == id && t.TenantId == tenantId, cancellationToken);

        return entity != null ? ConvertToSmsTemplate(entity) : null;
    }

    public async Task<SmsTemplate?> GetTemplateByNameAsync(string name, string tenantId, CancellationToken cancellationToken = default)
    {
        var entity = await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Name == name && t.TenantId == tenantId, cancellationToken);

        return entity != null ? ConvertToSmsTemplate(entity) : null;
    }

    public async Task<SmsTemplate> CreateTemplateAsync(TemplateRequest request, string tenantId, string? userId = null, CancellationToken cancellationToken = default)
    {
        var entity = new SmsTemplateEntity
        {
            Id = Guid.NewGuid(),
            TenantId = tenantId,
            Name = request.Name,
            DisplayName = request.DisplayName,
            Description = request.Description,
            Content = request.Content,
            Category = request.Category,
            Language = request.Language ?? "en",
            Tags = request.Tags,
            DefaultSenderId = request.DefaultSenderId,
            PreferredProvider = request.PreferredProvider,
            IsActive = true,
            CreatedBy = userId ?? GetCurrentUserId(),
            CreatedAt = DateTime.UtcNow,
            UpdatedBy = userId ?? GetCurrentUserId(),
            UpdatedAt = DateTime.UtcNow
        };

        _dbContext.SmsTemplates.Add(entity);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return ConvertToSmsTemplate(entity);
    }

    public async Task<SmsTemplate> UpdateTemplateAsync(Guid id, TemplateRequest request, string tenantId, string? userId = null, CancellationToken cancellationToken = default)
    {
        var entity = await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Id == id && t.TenantId == tenantId, cancellationToken);

        if (entity == null)
            throw new InvalidOperationException($"Template with ID {id} not found");

        entity.Name = request.Name;
        entity.DisplayName = request.DisplayName;
        entity.Description = request.Description;
        entity.Content = request.Content;
        entity.Category = request.Category;
        entity.Language = request.Language ?? entity.Language;
        entity.Tags = request.Tags;
        entity.DefaultSenderId = request.DefaultSenderId;
        entity.PreferredProvider = request.PreferredProvider;
        entity.UpdatedBy = userId ?? GetCurrentUserId();
        entity.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync(cancellationToken);

        return ConvertToSmsTemplate(entity);
    }

    public async Task<bool> DeleteTemplateAsync(Guid id, string tenantId, CancellationToken cancellationToken = default)
    {
        var entity = await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Id == id && t.TenantId == tenantId, cancellationToken);

        if (entity == null)
            return false;

        entity.IsActive = false;
        entity.UpdatedAt = DateTime.UtcNow;
        await _dbContext.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<TemplateProcessResult> ProcessTemplateAsync(Guid id, Dictionary<string, object> variables, string tenantId, CancellationToken cancellationToken = default)
    {
        var template = await GetTemplateAsync(id, tenantId, cancellationToken);
        if (template == null)
            throw new InvalidOperationException($"Template with ID {id} not found");

        return await ProcessTemplateContentAsync(template.Content, variables, cancellationToken);
    }

    public async Task<TemplateProcessResult> ProcessTemplateByNameAsync(string name, Dictionary<string, object> variables, string tenantId, CancellationToken cancellationToken = default)
    {
        var template = await GetTemplateByNameAsync(name, tenantId, cancellationToken);
        if (template == null)
            throw new InvalidOperationException($"Template with name '{name}' not found");

        return await ProcessTemplateContentAsync(template.Content, variables, cancellationToken);
    }

    public async Task<TemplateProcessResult> ProcessTemplateContentAsync(string content, Dictionary<string, object> variables, CancellationToken cancellationToken = default)
    {
        try
        {
            var template = Template.Parse(content);
            var hash = Hash.FromDictionary(variables);
            var result = template.Render(hash);

            return new TemplateProcessResult
            {
                ProcessedContent = result,
                Success = true,
                UsedVariables = variables,
                MissingVariables = new List<string>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing template content");
            return new TemplateProcessResult
            {
                ProcessedContent = content,
                Success = false,
                ErrorMessage = ex.Message,
                UsedVariables = variables,
                MissingVariables = new List<string>()
            };
        }
    }

    public async Task<TemplateTestResult> TestTemplateAsync(TemplateTestRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var processResult = await ProcessTemplateContentAsync(request.Content, request.Variables, cancellationToken);

            return new TemplateTestResult
            {
                Success = processResult.Success,
                ProcessedContent = processResult.ProcessedContent,
                ErrorMessage = processResult.ErrorMessage,
                UsedVariables = processResult.UsedVariables,
                MissingVariables = processResult.MissingVariables ?? new List<string>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing template");
            return new TemplateTestResult
            {
                Success = false,
                ProcessedContent = request.Content,
                ErrorMessage = ex.Message,
                UsedVariables = request.Variables,
                MissingVariables = new List<string>()
            };
        }
    }

    public async Task<ValidationResult> ValidateTemplateAsync(string content, CancellationToken cancellationToken = default)
    {
        try
        {
            Template.Parse(content);
            return new ValidationResult
            {
                IsValid = true,
                Errors = new List<string>(),
                Warnings = new List<string>()
            };
        }
        catch (Exception ex)
        {
            return new ValidationResult
            {
                IsValid = false,
                Errors = new List<string> { ex.Message },
                Warnings = new List<string>()
            };
        }
    }

    public async Task<List<string>> ExtractVariablesAsync(string content, CancellationToken cancellationToken = default)
    {
        try
        {
            var template = Template.Parse(content);
            // This is a simplified implementation - DotLiquid doesn't provide direct variable extraction
            // In a real implementation, you'd need to parse the template AST or use regex
            var variables = new List<string>();

            // Simple regex to find {{ variable }} patterns
            var matches = System.Text.RegularExpressions.Regex.Matches(content, @"\{\{\s*(\w+)\s*\}\}");
            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                if (match.Groups.Count > 1)
                {
                    var variable = match.Groups[1].Value;
                    if (!variables.Contains(variable))
                        variables.Add(variable);
                }
            }

            return variables;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting variables from template");
            return new List<string>();
        }
    }

    public async Task<TemplateUsageStats> GetTemplateUsageAsync(Guid id, string tenantId, CancellationToken cancellationToken = default)
    {
        // This would require tracking template usage in the database
        // For now, return a placeholder implementation
        return new TemplateUsageStats
        {
            TemplateId = id,
            TenantId = tenantId,
            TotalUsage = 0,
            UsageByProvider = new Dictionary<string, int>(),
            UsageByMonth = new Dictionary<string, int>(),
            LastUsed = null
        };
    }

    private SmsTemplate ConvertToSmsTemplate(SmsTemplateEntity entity)
    {
        return new SmsTemplate
        {
            Id = entity.Id,
            TenantId = entity.TenantId,
            Name = entity.Name,
            DisplayName = entity.DisplayName,
            Description = entity.Description,
            Content = entity.Content,
            Category = entity.Category,
            Language = entity.Language,
            Tags = entity.Tags,
            DefaultSenderId = entity.DefaultSenderId,
            PreferredProvider = entity.PreferredProvider,
            IsActive = entity.IsActive,
            CreatedBy = entity.CreatedBy,
            CreatedAt = entity.CreatedAt,
            UpdatedBy = entity.UpdatedBy,
            UpdatedAt = entity.UpdatedAt
        };
    }
}
