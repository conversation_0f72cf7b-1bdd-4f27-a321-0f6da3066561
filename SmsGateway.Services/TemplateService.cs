using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using DotLiquid;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Database;
using SmsGateway.Database.Entities;

namespace SmsGateway.Services;

/// <summary>
/// Template service implementation for SMS template management and processing
/// </summary>
public class TemplateService : ITemplateService
{
    private readonly SmsGatewayDbContext _dbContext;
    private readonly ILogger<TemplateService> _logger;

    public TemplateService(
        SmsGatewayDbContext dbContext,
        ILogger<TemplateService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<List<SmsTemplateEntity>> GetTemplatesAsync(CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        
        return await _dbContext.SmsTemplates
            .Where(t => t.TenantId == tenantId && t.IsActive)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<SmsTemplateEntity?> GetTemplateAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        
        return await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Id == id && t.TenantId == tenantId, cancellationToken);
    }

    public async Task<SmsTemplateEntity?> GetTemplateByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        
        return await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Name == name && t.TenantId == tenantId && t.IsActive, cancellationToken);
    }

    public async Task<SmsTemplateEntity> CreateTemplateAsync(CreateTemplateRequest request, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();

        // Check if template with same name exists
        var existingTemplate = await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Name == request.Name && t.TenantId == tenantId, cancellationToken);

        if (existingTemplate != null)
        {
            throw new InvalidOperationException($"Template with name '{request.Name}' already exists");
        }

        var template = new SmsTemplateEntity
        {
            Id = Guid.NewGuid(),
            TenantId = tenantId,
            Name = request.Name,
            DisplayName = request.DisplayName,
            Description = request.Description,
            Content = request.Content,
            Category = request.Category,
            Language = request.Language ?? "en",
            Tags = request.Tags,
            DefaultSenderId = request.DefaultSenderId,
            PreferredProvider = request.PreferredProvider,
            IsActive = true,
            CreatedBy = GetCurrentUserId(),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _dbContext.SmsTemplates.Add(template);
        await _dbContext.SaveChangesAsync(cancellationToken);

        return template;
    }

    public async Task<SmsTemplateEntity> UpdateTemplateAsync(Guid id, UpdateTemplateRequest request, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        
        var template = await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Id == id && t.TenantId == tenantId, cancellationToken);

        if (template == null)
        {
            throw new InvalidOperationException($"Template with ID '{id}' not found");
        }

        // Check if another template with same name exists
        if (!string.IsNullOrEmpty(request.Name) && request.Name != template.Name)
        {
            var existingTemplate = await _dbContext.SmsTemplates
                .FirstOrDefaultAsync(t => t.Name == request.Name && t.TenantId == tenantId && t.Id != id, cancellationToken);

            if (existingTemplate != null)
            {
                throw new InvalidOperationException($"Template with name '{request.Name}' already exists");
            }

            template.Name = request.Name;
        }

        if (!string.IsNullOrEmpty(request.DisplayName))
            template.DisplayName = request.DisplayName;

        if (request.Description != null)
            template.Description = request.Description;

        if (!string.IsNullOrEmpty(request.Content))
            template.Content = request.Content;

        if (request.Category != null)
            template.Category = request.Category;

        if (request.Language != null)
            template.Language = request.Language;

        if (request.Tags != null)
            template.Tags = request.Tags;

        if (request.DefaultSenderId != null)
            template.DefaultSenderId = request.DefaultSenderId;

        if (request.PreferredProvider != null)
            template.PreferredProvider = request.PreferredProvider;

        if (request.IsActive.HasValue)
            template.IsActive = request.IsActive.Value;

        template.UpdatedBy = GetCurrentUserId();
        template.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync(cancellationToken);

        return template;
    }

    public async Task<bool> DeleteTemplateAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var tenantId = GetCurrentTenantId();
        
        var template = await _dbContext.SmsTemplates
            .FirstOrDefaultAsync(t => t.Id == id && t.TenantId == tenantId, cancellationToken);

        if (template == null)
        {
            return false;
        }

        // Soft delete
        template.IsActive = false;
        template.UpdatedBy = GetCurrentUserId();
        template.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<string> ProcessTemplateAsync(Guid templateId, Dictionary<string, object> variables, CancellationToken cancellationToken = default)
    {
        var template = await GetTemplateAsync(templateId, cancellationToken);
        
        if (template == null)
        {
            throw new InvalidOperationException($"Template with ID '{templateId}' not found");
        }

        return ProcessTemplateContent(template.Content, variables);
    }

    public async Task<string> ProcessTemplateAsync(string templateName, Dictionary<string, object> variables, CancellationToken cancellationToken = default)
    {
        var template = await GetTemplateByNameAsync(templateName, cancellationToken);
        
        if (template == null)
        {
            throw new InvalidOperationException($"Template with name '{templateName}' not found");
        }

        return ProcessTemplateContent(template.Content, variables);
    }

    public string ProcessTemplateContent(string templateContent, Dictionary<string, object> variables)
    {
        try
        {
            var template = Template.Parse(templateContent);
            var hash = Hash.FromDictionary(variables);
            return template.Render(hash);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process template content: {TemplateContent}", templateContent);
            throw new InvalidOperationException($"Failed to process template: {ex.Message}", ex);
        }
    }

    public async Task<TemplateValidationResult> ValidateTemplateAsync(string templateContent, Dictionary<string, object>? testVariables = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var template = Template.Parse(templateContent);
            
            if (template.Errors.Any())
            {
                return new TemplateValidationResult
                {
                    IsValid = false,
                    Errors = template.Errors.Select(e => e.Message).ToList()
                };
            }

            // Test rendering if test variables provided
            if (testVariables != null)
            {
                var hash = Hash.FromDictionary(testVariables);
                var result = template.Render(hash);
                
                return new TemplateValidationResult
                {
                    IsValid = true,
                    TestOutput = result
                };
            }

            return new TemplateValidationResult
            {
                IsValid = true
            };
        }
        catch (Exception ex)
        {
            return new TemplateValidationResult
            {
                IsValid = false,
                Errors = new List<string> { ex.Message }
            };
        }
    }

    private string GetCurrentTenantId()
    {
        // TODO: Get from current context/claims
        return "default";
    }

    private string GetCurrentUserId()
    {
        // TODO: Get from current context/claims
        return "system";
    }
}

/// <summary>
/// Template validation result
/// </summary>
public class TemplateValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public string? TestOutput { get; set; }
}

/// <summary>
/// Create template request
/// </summary>
public class CreateTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Category { get; set; }
    public string? Language { get; set; }
    public string? Tags { get; set; }
    public string? DefaultSenderId { get; set; }
    public string? PreferredProvider { get; set; }
}

/// <summary>
/// Update template request
/// </summary>
public class UpdateTemplateRequest
{
    public string? Name { get; set; }
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public string? Content { get; set; }
    public string? Category { get; set; }
    public string? Language { get; set; }
    public string? Tags { get; set; }
    public string? DefaultSenderId { get; set; }
    public string? PreferredProvider { get; set; }
    public bool? IsActive { get; set; }
}
