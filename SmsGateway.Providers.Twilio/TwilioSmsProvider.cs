using System.Diagnostics;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models;

namespace SmsGateway.Providers.Twilio;

/// <summary>
/// Twilio SMS provider implementation
/// </summary>
public class TwilioSmsProvider : ISmsProvider
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<TwilioSmsProvider>? _logger;
    private ApiKeyProviderConfiguration? _configuration;

    public string Name => "Twilio";
    public string DisplayName => "Twilio SMS";
    public string Version => "1.0.0";
    public string Description => "Twilio SMS provider for sending SMS messages via Twilio API";
    public bool IsAvailable => _configuration != null && !string.IsNullOrEmpty(_configuration.ApiKey);

    public TwilioSmsProvider(HttpClient? httpClient = null, ILogger<TwilioSmsProvider>? logger = null)
    {
        _httpClient = httpClient ?? new HttpClient();
        _logger = logger;
    }

    public async Task InitializeAsync(ProviderConfiguration configuration)
    {
        if (configuration is not ApiKeyProviderConfiguration apiKeyConfig)
        {
            throw new ArgumentException("Twilio provider requires ApiKeyProviderConfiguration");
        }

        _configuration = apiKeyConfig;
        
        // Set default base URL if not provided
        if (string.IsNullOrEmpty(_configuration.BaseUrl))
        {
            _configuration.BaseUrl = "https://api.twilio.com/2010-04-01";
        }

        // Configure HTTP client
        _httpClient.BaseAddress = new Uri(_configuration.BaseUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(_configuration.TimeoutSeconds);
        
        // Set up basic authentication (Account SID and Auth Token)
        var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{GetAccountSid()}:{_configuration.ApiKey}"));
        _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authValue);

        _logger?.LogInformation("Twilio SMS provider initialized");
    }

    public async Task<SmsResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var accountSid = GetAccountSid();
            var requestBody = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("To", request.To),
                new KeyValuePair<string, string>("From", request.From ?? _configuration.DefaultSenderId ?? ""),
                new KeyValuePair<string, string>("Body", request.Message)
            });

            var response = await _httpClient.PostAsync($"/Accounts/{accountSid}/Messages.json", requestBody, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var twilioResponse = JsonSerializer.Deserialize<TwilioMessageResponse>(responseContent);
                return new SmsResponse
                {
                    Success = true,
                    MessageId = twilioResponse?.Sid,
                    Status = MapTwilioStatus(twilioResponse?.Status),
                    Cost = twilioResponse?.Price != null ? decimal.Parse(twilioResponse.Price) : null,
                    Currency = "USD",
                    Segments = twilioResponse?.NumSegments
                };
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<TwilioErrorResponse>(responseContent);
                return new SmsResponse
                {
                    Success = false,
                    Status = SmsStatus.Failed,
                    ErrorMessage = errorResponse?.Message ?? "Unknown error",
                    ErrorCode = errorResponse?.Code?.ToString()
                };
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to send SMS via Twilio");
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "TWILIO_EXCEPTION"
            };
        }
    }

    public async Task<SmsStatus> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var accountSid = GetAccountSid();
            var response = await _httpClient.GetAsync($"/Accounts/{accountSid}/Messages/{messageId}.json", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var twilioResponse = JsonSerializer.Deserialize<TwilioMessageResponse>(responseContent);
                return MapTwilioStatus(twilioResponse?.Status);
            }
            
            return SmsStatus.Unknown;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to get message status from Twilio for message {MessageId}", messageId);
            return SmsStatus.Unknown;
        }
    }

    public bool SupportsCountry(string countryCode)
    {
        // Twilio supports most countries - check configuration or return true for all
        return _configuration?.SupportedCountries.Count == 0 || 
               _configuration?.SupportedCountries.Contains(countryCode, StringComparer.OrdinalIgnoreCase) == true;
    }

    public async Task<(decimal cost, string currency)?> GetEstimatedCostAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        // Twilio pricing varies by destination - this is a simplified implementation
        // In production, you would call Twilio's pricing API
        return (0.0075m, "USD"); // Average cost per SMS
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(ProviderConfiguration configuration)
    {
        var result = new ValidationResult { IsValid = true };

        if (configuration is not ApiKeyProviderConfiguration apiKeyConfig)
        {
            result.IsValid = false;
            result.Errors.Add("Configuration must be of type ApiKeyProviderConfiguration");
            return result;
        }

        if (string.IsNullOrEmpty(apiKeyConfig.ApiKey))
        {
            result.IsValid = false;
            result.Errors.Add("ApiKey (Auth Token) is required");
        }

        if (string.IsNullOrEmpty(GetAccountSidFromConfig(apiKeyConfig)))
        {
            result.IsValid = false;
            result.Errors.Add("Account SID must be provided in Settings");
        }

        return result;
    }

    public async Task<HealthCheckResult> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            return new HealthCheckResult
            {
                IsHealthy = false,
                Message = "Provider not initialized"
            };
        }

        var stopwatch = Stopwatch.StartNew();
        try
        {
            var accountSid = GetAccountSid();
            var response = await _httpClient.GetAsync($"/Accounts/{accountSid}.json", cancellationToken);
            stopwatch.Stop();

            return new HealthCheckResult
            {
                IsHealthy = response.IsSuccessStatusCode,
                Message = response.IsSuccessStatusCode ? "Healthy" : $"HTTP {response.StatusCode}",
                ResponseTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return new HealthCheckResult
            {
                IsHealthy = false,
                Message = ex.Message,
                ResponseTime = stopwatch.Elapsed
            };
        }
    }

    public async Task DisposeAsync()
    {
        _httpClient?.Dispose();
        _logger?.LogInformation("Twilio SMS provider disposed");
        await Task.CompletedTask;
    }

    private string GetAccountSid()
    {
        return GetAccountSidFromConfig(_configuration!);
    }

    private string GetAccountSidFromConfig(ApiKeyProviderConfiguration config)
    {
        return config.Settings.TryGetValue("AccountSid", out var accountSid) ? accountSid.ToString() ?? "" : "";
    }

    private SmsStatus MapTwilioStatus(string? twilioStatus)
    {
        return twilioStatus?.ToLowerInvariant() switch
        {
            "queued" => SmsStatus.Queued,
            "sending" => SmsStatus.Sent,
            "sent" => SmsStatus.Sent,
            "delivered" => SmsStatus.Delivered,
            "undelivered" => SmsStatus.Undelivered,
            "failed" => SmsStatus.Failed,
            _ => SmsStatus.Unknown
        };
    }

    private class TwilioMessageResponse
    {
        public string? Sid { get; set; }
        public string? Status { get; set; }
        public string? Price { get; set; }
        public int? NumSegments { get; set; }
    }

    private class TwilioErrorResponse
    {
        public int? Code { get; set; }
        public string? Message { get; set; }
    }
}
