using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models;

namespace SmsGateway.Providers.Twilio;

/// <summary>
/// Plugin factory for Twilio SMS provider
/// </summary>
public class TwilioPluginFactory : PluginFactoryBase
{
    public override PluginMetadata Metadata => new()
    {
        AssemblyName = "SmsGateway.Providers.Twilio",
        Version = "1.0.0",
        Author = "SMS Gateway Team",
        Description = "Twilio SMS provider for sending SMS via Twilio API",
        Tags = { "twilio", "sms", "production" },
        MinimumCoreVersion = "1.0.0",
        Dependencies = { "System.Text.Json" }
    };

    public override IEnumerable<ISmsProvider> CreateProviders()
    {
        // Create HTTP client and logger if available (will be null in plugin context)
        var httpClient = new HttpClient();
        ILogger<TwilioSmsProvider>? logger = null;
        
        yield return new TwilioSmsProvider(httpClient, logger);
    }

    public override IEnumerable<Type> GetSupportedConfigurationTypes()
    {
        return new[] { typeof(ApiKeyProviderConfiguration) };
    }

    public override ValidationResult ValidateDependencies()
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // Check if required types are available
            var httpClientType = typeof(HttpClient);
            var jsonSerializerType = typeof(System.Text.Json.JsonSerializer);
            
            if (httpClientType == null || jsonSerializerType == null)
            {
                result.IsValid = false;
                result.Errors.Add("Required dependencies not available");
            }
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Dependency validation failed: {ex.Message}");
        }

        return result;
    }
}
