Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Api", "SmsGateway.api\SmsGateway.api.csproj", "{583F1C8C-0352-4681-B367-BBECC5C3528C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Core", "SmsGateway.Core\SmsGateway.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Database", "SmsGateway.Database\SmsGateway.Database.csproj", "{B2C3D4E5-F6A7-8901-BCDE-F23456789012}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Services", "SmsGateway.Services\SmsGateway.Services.csproj", "{C3D4E5F6-A7B8-9012-CDEF-345678901234}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Providers.Mock", "SmsGateway.Providers.Mock\SmsGateway.Providers.Mock.csproj", "{D4E5F6A7-B8C9-0123-DEF4-456789012345}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Providers.Twilio", "SmsGateway.Providers.Twilio\SmsGateway.Providers.Twilio.csproj", "{E5F6A7B8-C9D0-1234-EF56-567890123456}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Providers.BulkSms", "SmsGateway.Providers.BulkSms\SmsGateway.Providers.BulkSms.csproj", "{F6A7B8C9-D0E1-2345-F678-678901234567}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Portal", "SmsGateway.Portal\SmsGateway.Portal.csproj", "{A7B8C9D0-E1F2-3456-789A-789012345678}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Build", "Build", "{31F905FE-214E-421D-93B2-EF95071716F6}"
	ProjectSection(SolutionItems) = preProject
		build\sms-gateway.ruleset = build\sms-gateway.ruleset
		build\Directory.Build.props = build\Directory.Build.props
		build\Directory.Build.targets = build\Directory.Build.targets
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Providers", "Providers", "{B9740C83-D7F6-4B7E-8396-671485393441}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{583F1C8C-0352-4681-B367-BBECC5C3528C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{583F1C8C-0352-4681-B367-BBECC5C3528C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{583F1C8C-0352-4681-B367-BBECC5C3528C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{583F1C8C-0352-4681-B367-BBECC5C3528C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6A7-8901-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6A7-8901-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6A7-8901-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6A7-8901-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-A7B8-9012-CDEF-345678901234}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-A7B8-9012-CDEF-345678901234}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-A7B8-9012-CDEF-345678901234}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-A7B8-9012-CDEF-345678901234}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6A7-B8C9-0123-DEF4-456789012345}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6A7-B8C9-0123-DEF4-456789012345}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6A7-B8C9-0123-DEF4-456789012345}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6A7-B8C9-0123-DEF4-456789012345}.Release|Any CPU.Build.0 = Release|Any CPU
		{E5F6A7B8-C9D0-1234-EF56-567890123456}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6A7B8-C9D0-1234-EF56-567890123456}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6A7B8-C9D0-1234-EF56-567890123456}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6A7B8-C9D0-1234-EF56-567890123456}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6A7B8C9-D0E1-2345-F678-678901234567}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6A7B8C9-D0E1-2345-F678-678901234567}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6A7B8C9-D0E1-2345-F678-678901234567}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6A7B8C9-D0E1-2345-F678-678901234567}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7B8C9D0-E1F2-3456-789A-789012345678}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7B8C9D0-E1F2-3456-789A-789012345678}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7B8C9D0-E1F2-3456-789A-789012345678}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7B8C9D0-E1F2-3456-789A-789012345678}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E5F6A7B8-C9D0-1234-EF56-567890123456} = {B9740C83-D7F6-4B7E-8396-671485393441}
		{D4E5F6A7-B8C9-0123-DEF4-456789012345} = {B9740C83-D7F6-4B7E-8396-671485393441}
		{F6A7B8C9-D0E1-2345-F678-678901234567} = {B9740C83-D7F6-4B7E-8396-671485393441}
	EndGlobalSection
EndGlobal
