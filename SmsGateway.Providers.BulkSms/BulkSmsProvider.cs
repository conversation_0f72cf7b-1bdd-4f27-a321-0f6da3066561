using System.Diagnostics;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models;

namespace SmsGateway.Providers.BulkSms;

/// <summary>
/// BulkSMS provider implementation
/// </summary>
public class BulkSmsProvider : ISmsProvider
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<BulkSmsProvider>? _logger;
    private UsernamePasswordProviderConfiguration? _configuration;

    public string Name => "BulkSMS";
    public string DisplayName => "BulkSMS";
    public string Version => "1.0.0";
    public string Description => "BulkSMS provider for sending SMS messages via BulkSMS API";
    public bool IsAvailable => _configuration != null && !string.IsNullOrEmpty(_configuration.Username) && !string.IsNullOrEmpty(_configuration.Password);

    public BulkSmsProvider(HttpClient? httpClient = null, ILogger<BulkSmsProvider>? logger = null)
    {
        _httpClient = httpClient ?? new HttpClient();
        _logger = logger;
    }

    public async Task InitializeAsync(ProviderConfiguration configuration)
    {
        if (configuration is not UsernamePasswordProviderConfiguration usernamePasswordConfig)
        {
            throw new ArgumentException("BulkSMS provider requires UsernamePasswordProviderConfiguration");
        }

        _configuration = usernamePasswordConfig;
        
        // Set default base URL if not provided
        if (string.IsNullOrEmpty(_configuration.BaseUrl))
        {
            _configuration.BaseUrl = "https://api.bulksms.com/v1";
        }

        // Configure HTTP client
        _httpClient.BaseAddress = new Uri(_configuration.BaseUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(_configuration.TimeoutSeconds);
        
        // Set up basic authentication
        var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_configuration.Username}:{_configuration.Password}"));
        _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authValue);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "SmsGateway/1.0");

        _logger?.LogInformation("BulkSMS provider initialized");
    }

    public async Task<SmsResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var bulkSmsRequest = new BulkSmsRequest
            {
                To = request.To,
                From = request.From ?? _configuration.DefaultSenderId ?? "",
                Body = request.Message,
                DeliveryReports = "ALL"
            };

            var jsonContent = JsonSerializer.Serialize(new[] { bulkSmsRequest }, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync("/messages", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var bulkSmsResponses = JsonSerializer.Deserialize<BulkSmsResponse[]>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var bulkSmsResponse = bulkSmsResponses?.FirstOrDefault();
                if (bulkSmsResponse != null)
                {
                    return new SmsResponse
                    {
                        Success = true,
                        MessageId = bulkSmsResponse.Id,
                        Status = MapBulkSmsStatus(bulkSmsResponse.Status?.Type),
                        Cost = bulkSmsResponse.CreditCost,
                        Currency = "Credits",
                        Segments = bulkSmsResponse.Encoding == "UNICODE" ? 
                            (int)Math.Ceiling((double)request.Message.Length / 70) : 
                            (int)Math.Ceiling((double)request.Message.Length / 160)
                    };
                }
            }

            // Handle error response
            var errorResponse = JsonSerializer.Deserialize<BulkSmsErrorResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = errorResponse?.Detail ?? $"HTTP {response.StatusCode}",
                ErrorCode = errorResponse?.Type ?? response.StatusCode.ToString()
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to send SMS via BulkSMS");
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "BULKSMS_EXCEPTION"
            };
        }
    }

    public async Task<SmsStatus> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var response = await _httpClient.GetAsync($"/messages/{messageId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var bulkSmsResponse = JsonSerializer.Deserialize<BulkSmsResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                
                return MapBulkSmsStatus(bulkSmsResponse?.Status?.Type);
            }
            
            return SmsStatus.Unknown;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to get message status from BulkSMS for message {MessageId}", messageId);
            return SmsStatus.Unknown;
        }
    }

    public bool SupportsCountry(string countryCode)
    {
        // BulkSMS supports most countries - check configuration or return true for all
        return _configuration?.SupportedCountries.Count == 0 || 
               _configuration?.SupportedCountries.Contains(countryCode, StringComparer.OrdinalIgnoreCase) == true;
    }

    public async Task<(decimal cost, string currency)?> GetEstimatedCostAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        // BulkSMS uses credit-based pricing - this is a simplified implementation
        // In production, you would call BulkSMS's pricing API or calculate based on destination
        var segments = CalculateSegments(request.Message);
        var costPerSegment = 1.0m; // 1 credit per segment (approximate)
        
        return (segments * costPerSegment, "Credits");
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(ProviderConfiguration configuration)
    {
        var result = new ValidationResult { IsValid = true };

        if (configuration is not UsernamePasswordProviderConfiguration usernamePasswordConfig)
        {
            result.IsValid = false;
            result.Errors.Add("Configuration must be of type UsernamePasswordProviderConfiguration");
            return result;
        }

        if (string.IsNullOrEmpty(usernamePasswordConfig.Username))
        {
            result.IsValid = false;
            result.Errors.Add("Username is required");
        }

        if (string.IsNullOrEmpty(usernamePasswordConfig.Password))
        {
            result.IsValid = false;
            result.Errors.Add("Password is required");
        }

        return result;
    }

    public async Task<HealthCheckResult> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            return new HealthCheckResult
            {
                IsHealthy = false,
                Message = "Provider not initialized"
            };
        }

        var stopwatch = Stopwatch.StartNew();
        try
        {
            // Check account balance as a health check
            var response = await _httpClient.GetAsync("/profile", cancellationToken);
            stopwatch.Stop();

            return new HealthCheckResult
            {
                IsHealthy = response.IsSuccessStatusCode,
                Message = response.IsSuccessStatusCode ? "Healthy" : $"HTTP {response.StatusCode}",
                ResponseTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return new HealthCheckResult
            {
                IsHealthy = false,
                Message = ex.Message,
                ResponseTime = stopwatch.Elapsed
            };
        }
    }

    public async Task DisposeAsync()
    {
        _httpClient?.Dispose();
        _logger?.LogInformation("BulkSMS provider disposed");
        await Task.CompletedTask;
    }

    private SmsStatus MapBulkSmsStatus(string? bulkSmsStatus)
    {
        return bulkSmsStatus?.ToUpperInvariant() switch
        {
            "ACCEPTED" => SmsStatus.Queued,
            "SCHEDULED" => SmsStatus.Queued,
            "SENT" => SmsStatus.Sent,
            "DELIVERED" => SmsStatus.Delivered,
            "UNKNOWN" => SmsStatus.Unknown,
            "REJECTED" => SmsStatus.Failed,
            "FAILED" => SmsStatus.Failed,
            _ => SmsStatus.Unknown
        };
    }

    private int CalculateSegments(string message)
    {
        // SMS segments calculation for BulkSMS
        var containsUnicode = message.Any(c => c > 127);
        var maxLength = containsUnicode ? 70 : 160;
        return (int)Math.Ceiling((double)message.Length / maxLength);
    }

    // BulkSMS API models
    private class BulkSmsRequest
    {
        public string To { get; set; } = string.Empty;
        public string From { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public string DeliveryReports { get; set; } = "ALL";
    }

    private class BulkSmsResponse
    {
        public string? Id { get; set; }
        public string? Type { get; set; }
        public string? From { get; set; }
        public string? To { get; set; }
        public string? Body { get; set; }
        public string? Encoding { get; set; }
        public decimal? CreditCost { get; set; }
        public BulkSmsStatus? Status { get; set; }
    }

    private class BulkSmsStatus
    {
        public string? Id { get; set; }
        public string? Type { get; set; }
        public string? Subtype { get; set; }
    }

    private class BulkSmsErrorResponse
    {
        public string? Type { get; set; }
        public string? Title { get; set; }
        public string? Detail { get; set; }
        public int? Status { get; set; }
    }
}
