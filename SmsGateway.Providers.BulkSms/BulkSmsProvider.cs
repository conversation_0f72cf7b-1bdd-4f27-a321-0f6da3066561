using System.ComponentModel.DataAnnotations;
       using System.Diagnostics;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Common;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Providers.BulkSms.Models;

namespace SmsGateway.Providers.BulkSms;

/// <summary>
/// Enhanced BulkSMS provider implementation with comprehensive features
/// </summary>
public class BulkSmsProvider : ISmsProvider
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<BulkSmsProvider>? _logger;
    private UsernamePasswordProviderConfiguration? _configuration;
    private readonly Dictionary<string, int> _rateLimitTracker = new();
    private readonly object _rateLimitLock = new();

    #region Basic Properties
    public string Name => "BulkSMS";
    public string DisplayName => "BulkSMS Enhanced";
    public string Version => "2.0.0";
    public string Description => "Enhanced BulkSMS provider with comprehensive SMS features including delivery reports, scheduling, MMS, and advanced analytics";
    public string Author => "SMS Gateway Team";
    public string? WebsiteUrl => "https://www.bulksms.com";
    public string? DocumentationUrl => "https://developer.bulksms.com/json/v1/";
    public bool IsAvailable => _configuration != null && !string.IsNullOrEmpty(_configuration.Username) && !string.IsNullOrEmpty(_configuration.Password);

    public ProviderCapabilities Capabilities => new()
    {
        SupportedFeatures = new List<ProviderFeature>
        {
            ProviderFeature.BasicSms,
            ProviderFeature.BulkSms,
            ProviderFeature.ScheduledSms,
            ProviderFeature.DeliveryReports,
            ProviderFeature.Unicode,
            ProviderFeature.LongMessages,
            ProviderFeature.AlphanumericSenderId,
            ProviderFeature.CustomSenderId,
            ProviderFeature.Webhooks,
            ProviderFeature.RealTimeStatus,
            ProviderFeature.Analytics,
            ProviderFeature.OptOutManagement
        },
        SupportedMessageTypes = new List<MessageType>
        {
            MessageType.Text,
            MessageType.Unicode,
            MessageType.Flash
        },
        SupportedCountries = new List<string>
        {
            "US", "CA", "GB", "AU", "ZA", "DE", "FR", "ES", "IT", "NL", "BE", "CH", "AT", "SE", "NO", "DK", "FI"
        },
        SupportedCharsets = new List<string> { "UTF-8", "GSM7", "UCS2" },
        MaxSingleSmsLength = 160,
        MaxUnicodeSmsLength = 70,
        MaxConcatenatedSmsLength = 1600,
        MaxConcatenatedParts = 10,
        SupportsCustomSenderId = true,
        SupportsAlphanumericSenderId = true,
        MaxSenderIdLength = 11,
        SupportsDeliveryReports = true,
        SupportsScheduledSending = true,
        MaxSchedulingHours = 24 * 365, // 1 year
        SupportsBulkSending = true,
        MaxBulkSize = 50000,
        SupportsTwoWay = true,
        SupportsMms = false
    };

    public ProviderLimits Limits => new()
    {
        RateLimitPerSecond = 10,
        RateLimitPerMinute = 300,
        RateLimitPerHour = 10000,
        RateLimitPerDay = 100000,
        MaxConcurrentConnections = 10,
        RequestTimeoutSeconds = 30,
        MaxRetryAttempts = 3,
        RetryDelaySeconds = 5,
        MinimumBalance = 1.0m,
        LowBalanceThreshold = 10.0m
    };
    #endregion

    public BulkSmsProvider(HttpClient? httpClient = null, ILogger<BulkSmsProvider>? logger = null)
    {
        _httpClient = httpClient ?? new HttpClient();
        _logger = logger;
    }

    #region Core SMS Operations
    public async Task InitializeAsync(ProviderConfiguration configuration)
    {
        if (configuration is not UsernamePasswordProviderConfiguration usernamePasswordConfig)
        {
            throw new ArgumentException("BulkSMS provider requires UsernamePasswordProviderConfiguration");
        }

        _configuration = usernamePasswordConfig;
        
        // Set default base URL if not provided
        if (string.IsNullOrEmpty(_configuration.BaseUrl))
        {
            _configuration.BaseUrl = "https://api.bulksms.com/v1";
        }

        // Configure HTTP client
        _httpClient.BaseAddress = new Uri(_configuration.BaseUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(_configuration.TimeoutSeconds);
        
        // Set up basic authentication
        var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_configuration.Username}:{_configuration.Password}"));
        _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authValue);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "SmsGateway-Enhanced/2.0");

        _logger?.LogInformation("Enhanced BulkSMS provider initialized with advanced features");
    }

    public async Task<SmsResponse> SendSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        await CheckRateLimitAsync();

        try
        {
            var bulkSmsRequest = new BulkSmsApiRequest
            {
                To = request.To,
                From = request.From ?? _configuration.DefaultSenderId ?? "",
                Body = request.Message,
                DeliveryReports = "ALL",
                ProtocolId = request.Priority == SmsPriority.Critical ? 1 : 0,
                UserSuppliedId = request.Reference,
                SendTime = request.ScheduledAt?.ToString("yyyy-MM-ddTHH:mm:ssZ")
            };

            var jsonContent = JsonSerializer.Serialize(new[] { bulkSmsRequest }, JsonOptions);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/messages", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var bulkSmsResponses = JsonSerializer.Deserialize<BulkSmsApiResponse[]>(responseContent, JsonOptions);
                var bulkSmsResponse = bulkSmsResponses?.FirstOrDefault();
                
                if (bulkSmsResponse != null)
                {
                    return new SmsResponse
                    {
                        Success = true,
                        MessageId = bulkSmsResponse.Id,
                        Status = MapBulkSmsStatus(bulkSmsResponse.Status?.Type),
                        Cost = bulkSmsResponse.CreditCost,
                        Currency = "Credits",
                        Segments = CalculateSegments(request.Message),
                        Provider = Name,
                        Timestamp = DateTime.UtcNow,
                        Metadata = new Dictionary<string, object>
                        {
                            ["encoding"] = bulkSmsResponse.Encoding ?? "GSM7",
                            ["messageClass"] = bulkSmsResponse.MessageClass ?? 1,
                            ["protocolId"] = bulkSmsResponse.ProtocolId ?? 0
                        }
                    };
                }
            }

            // Handle error response
            var errorResponse = await ParseErrorResponseAsync(responseContent);
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = errorResponse.Detail ?? $"HTTP {response.StatusCode}",
                ErrorCode = errorResponse.Type ?? response.StatusCode.ToString(),
                Provider = Name,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to send SMS via BulkSMS");
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "BULKSMS_EXCEPTION",
                Provider = Name,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<BulkSmsResponse> SendBulkSmsAsync(SendBulkSmsRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        var responses = new List<SmsResponse>();
        var batchSize = Math.Min(request.Messages.Count, Capabilities.MaxBulkSize);
        
        for (int i = 0; i < request.Messages.Count; i += batchSize)
        {
            var batch = request.Messages.Skip(i).Take(batchSize).ToList();
            var batchRequests = batch.Select(msg => new BulkSmsApiRequest
            {
                To = msg.To,
                From = msg.From ?? request.DefaultFrom ?? _configuration.DefaultSenderId ?? "",
                Body = msg.Message,
                DeliveryReports = "ALL",
                ProtocolId = msg.Priority == SmsPriority.Critical ? 1 : 0,
                UserSuppliedId = msg.Reference,
                SendTime = msg.ScheduledAt?.ToString("yyyy-MM-ddTHH:mm:ssZ")
            }).ToArray();

            try
            {
                await CheckRateLimitAsync();
                
                var jsonContent = JsonSerializer.Serialize(batchRequests, JsonOptions);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/messages", content, cancellationToken);
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var bulkSmsResponses = JsonSerializer.Deserialize<BulkSmsApiResponse[]>(responseContent, JsonOptions);
                    
                    if (bulkSmsResponses != null)
                    {
                        for (int j = 0; j < bulkSmsResponses.Length && j < batch.Count; j++)
                        {
                            var bulkSmsResponse = bulkSmsResponses[j];
                            var originalRequest = batch[j];
                            
                            responses.Add(new SmsResponse
                            {
                                Success = true,
                                MessageId = bulkSmsResponse.Id,
                                Status = MapBulkSmsStatus(bulkSmsResponse.Status?.Type),
                                Cost = bulkSmsResponse.CreditCost,
                                Currency = "Credits",
                                Segments = CalculateSegments(originalRequest.Message),
                                Provider = Name,
                                Timestamp = DateTime.UtcNow
                            });
                        }
                    }
                }
                else
                {
                    // Handle batch error
                    var errorResponse = await ParseErrorResponseAsync(responseContent);
                    foreach (var msg in batch)
                    {
                        responses.Add(new SmsResponse
                        {
                            Success = false,
                            Status = SmsStatus.Failed,
                            ErrorMessage = errorResponse.Detail ?? $"HTTP {response.StatusCode}",
                            ErrorCode = errorResponse.Type ?? response.StatusCode.ToString(),
                            Provider = Name,
                            Timestamp = DateTime.UtcNow
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to send bulk SMS batch via BulkSMS");
                foreach (var msg in batch)
                {
                    responses.Add(new SmsResponse
                    {
                        Success = false,
                        Status = SmsStatus.Failed,
                        ErrorMessage = ex.Message,
                        ErrorCode = "BULKSMS_BATCH_EXCEPTION",
                        Provider = Name,
                        Timestamp = DateTime.UtcNow
                    });
                }
            }
        }

        var successfulCount = responses.Count(r => r.Success);
        var totalCost = responses.Where(r => r.Cost.HasValue).Sum(r => r.Cost.Value);

        return new BulkSmsResponse
        {
            Success = successfulCount > 0,
            TotalMessages = responses.Count,
            SuccessfulMessages = successfulCount,
            FailedMessages = responses.Count - successfulCount,
            MessageResponses = responses,
            TotalCost = totalCost,
            Currency = "Credits",
            Timestamp = DateTime.UtcNow
        };
    }

    public async Task<SmsResponse> ScheduleSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default)
    {
        if (!request.ScheduledAt.HasValue)
        {
            request.ScheduledAt = DateTime.UtcNow.AddMinutes(5); // Default to 5 minutes from now
        }

        return await SendSmsAsync(request, cancellationToken);
    }

    public async Task<bool> CancelScheduledSmsAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var response = await _httpClient.DeleteAsync($"/messages/{messageId}", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to cancel scheduled SMS {MessageId}", messageId);
            return false;
        }
    }
    #endregion

    #region Helper Methods
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true
    };

    private async Task CheckRateLimitAsync()
    {
        lock (_rateLimitLock)
        {
            var currentMinute = DateTime.UtcNow.ToString("yyyy-MM-dd-HH-mm");
            if (!_rateLimitTracker.ContainsKey(currentMinute))
            {
                _rateLimitTracker.Clear();
                _rateLimitTracker[currentMinute] = 0;
            }

            if (_rateLimitTracker[currentMinute] >= (Limits.RateLimitPerMinute ?? 300))
            {
                throw new InvalidOperationException("Rate limit exceeded");
            }

            _rateLimitTracker[currentMinute]++;
        }
    }

    private SmsStatus MapBulkSmsStatus(string? bulkSmsStatus)
    {
        return bulkSmsStatus?.ToUpperInvariant() switch
        {
            "ACCEPTED" => SmsStatus.Queued,
            "SCHEDULED" => SmsStatus.Scheduled,
            "SENT" => SmsStatus.Sent,
            "DELIVERED" => SmsStatus.Delivered,
            "UNKNOWN" => SmsStatus.Unknown,
            "REJECTED" => SmsStatus.Rejected,
            "FAILED" => SmsStatus.Failed,
            "EXPIRED" => SmsStatus.Failed,
            _ => SmsStatus.Unknown
        };
    }

    private int CalculateSegments(string message)
    {
        var containsUnicode = message.Any(c => c > 127);
        var maxLength = containsUnicode ? 70 : 160;
        return (int)Math.Ceiling((double)message.Length / maxLength);
    }

    private async Task<BulkSmsErrorResponse> ParseErrorResponseAsync(string responseContent)
    {
        try
        {
            return JsonSerializer.Deserialize<BulkSmsErrorResponse>(responseContent, JsonOptions) ?? new BulkSmsErrorResponse();
        }
        catch
        {
            return new BulkSmsErrorResponse { Detail = responseContent };
        }
    }
    #endregion

    #region Message Status and Tracking
    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var response = await _httpClient.GetAsync($"/messages/{messageId}", cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var bulkSmsResponse = JsonSerializer.Deserialize<BulkSmsApiResponse>(responseContent, JsonOptions);

                if (bulkSmsResponse != null)
                {
                    return new MessageStatusResponse
                    {
                        MessageId = messageId,
                        Status = MapBulkSmsStatus(bulkSmsResponse.Status?.Type),
                        StatusDescription = bulkSmsResponse.Status?.Description,
                        Provider = Name,
                        SentAt = bulkSmsResponse.SubmissionDate,
                        DeliveredAt = bulkSmsResponse.Status?.Type == "DELIVERED" ? DateTime.UtcNow : null,
                        LastUpdated = DateTime.UtcNow,
                        Metadata = new Dictionary<string, object>
                        {
                            ["encoding"] = bulkSmsResponse.Encoding ?? "GSM7",
                            ["creditCost"] = bulkSmsResponse.CreditCost ?? 0
                        }
                    };
                }
            }

            return new MessageStatusResponse
            {
                MessageId = messageId,
                Status = SmsStatus.Unknown,
                StatusDescription = "Message not found",
                Provider = Name,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to get message status for {MessageId}", messageId);
            return new MessageStatusResponse
            {
                MessageId = messageId,
                Status = SmsStatus.Unknown,
                StatusDescription = ex.Message,
                Provider = Name,
                LastUpdated = DateTime.UtcNow
            };
        }
    }

    public async Task<MessageDetailsResponse> GetMessageDetailsAsync(string messageId, CancellationToken cancellationToken = default)
    {
        var statusResponse = await GetMessageStatusAsync(messageId, cancellationToken);

        return new MessageDetailsResponse
        {
            MessageId = messageId,
            Status = statusResponse.Status,
            Provider = Name,
            CreatedAt = statusResponse.SentAt ?? DateTime.UtcNow,
            SentAt = statusResponse.SentAt,
            DeliveredAt = statusResponse.DeliveredAt,
            ErrorMessage = statusResponse.Status == SmsStatus.Failed ? statusResponse.StatusDescription : null,
            Metadata = statusResponse.Metadata
        };
    }

    public async Task<DeliveryReportResponse> GetDeliveryReportAsync(string messageId, CancellationToken cancellationToken = default)
    {
        var statusResponse = await GetMessageStatusAsync(messageId, cancellationToken);

        return new DeliveryReportResponse
        {
            MessageId = messageId,
            Status = statusResponse.Status,
            DeliveredAt = statusResponse.DeliveredAt,
            ErrorCode = statusResponse.Status == SmsStatus.Failed ? "DELIVERY_FAILED" : null,
            ErrorDescription = statusResponse.Status == SmsStatus.Failed ? statusResponse.StatusDescription : null,
            IsFinalStatus = statusResponse.Status is SmsStatus.Delivered or SmsStatus.Failed or SmsStatus.Rejected
        };
    }

    public async Task<List<MessageStatusResponse>> GetBulkMessageStatusAsync(List<string> messageIds, CancellationToken cancellationToken = default)
    {
        var tasks = messageIds.Select(id => GetMessageStatusAsync(id, cancellationToken));
        var results = await Task.WhenAll(tasks);
        return results.ToList();
    }
    #endregion

    #region Cost and Pricing
    public async Task<CostEstimateResponse> GetEstimatedCostAsync(GetCostEstimateRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            // BulkSMS uses a credit system, estimate based on message segments
            var segments = CalculateSegments(request.Message);
            var estimatedCredits = segments * 1.0m; // 1 credit per segment (approximate)

            // Try to get actual pricing from API
            var response = await _httpClient.GetAsync($"/pricing?country={GetCountryFromPhoneNumber(request.To)}", cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var pricingData = JsonSerializer.Deserialize<BulkSmsPricingResponse>(responseContent, JsonOptions);

                if (pricingData?.Price != null)
                {
                    estimatedCredits = pricingData.Price.Value * segments;
                }
            }

            return new CostEstimateResponse
            {
                Cost = estimatedCredits,
                Currency = "Credits",
                Provider = Name,
                Segments = segments,
                CostPerSegment = estimatedCredits / segments,
                Country = GetCountryFromPhoneNumber(request.To),
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to get cost estimate");
            return new CostEstimateResponse
            {
                Cost = CalculateSegments(request.Message),
                Currency = "Credits",
                Provider = Name,
                Segments = CalculateSegments(request.Message),
                CostPerSegment = 1.0m,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<PricingInfoResponse> GetPricingInfoAsync(string? countryCode = null, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var url = countryCode != null ? $"/pricing?country={countryCode}" : "/pricing";
            var response = await _httpClient.GetAsync(url, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var pricingData = JsonSerializer.Deserialize<BulkSmsPricingResponse>(responseContent, JsonOptions);

                return new PricingInfoResponse
                {
                    CountryCode = countryCode,
                    PricePerSms = pricingData?.Price ?? 1.0m,
                    Currency = "Credits",
                    EffectiveDate = DateTime.UtcNow
                };
            }

            return new PricingInfoResponse
            {
                CountryCode = countryCode,
                PricePerSms = 1.0m,
                Currency = "Credits",
                EffectiveDate = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to get pricing info");
            return new PricingInfoResponse
            {
                CountryCode = countryCode,
                PricePerSms = 1.0m,
                Currency = "Credits",
                EffectiveDate = DateTime.UtcNow
            };
        }
    }

    public async Task<AccountBalanceResponse> GetAccountBalanceAsync(CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var response = await _httpClient.GetAsync("/profile", cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var profileData = JsonSerializer.Deserialize<BulkSmsProfileResponse>(responseContent, JsonOptions);

                return new AccountBalanceResponse
                {
                    Balance = profileData?.Credits?.Balance ?? 0,
                    Currency = "Credits",
                    CreditBalance = profileData?.Credits?.Balance,
                    LowBalanceThreshold = Limits.LowBalanceThreshold,
                    IsLowBalance = (profileData?.Credits?.Balance ?? 0) < (Limits.LowBalanceThreshold ?? 10),
                    LastUpdated = DateTime.UtcNow
                };
            }

            return new AccountBalanceResponse
            {
                Balance = 0,
                Currency = "Credits",
                IsLowBalance = true,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to get account balance");
            return new AccountBalanceResponse
            {
                Balance = 0,
                Currency = "Credits",
                IsLowBalance = true,
                LastUpdated = DateTime.UtcNow
            };
        }
    }
    #endregion

    #region Capabilities and Validation
    public bool SupportsCountry(string countryCode)
    {
        return Capabilities.SupportedCountries.Count == 0 ||
               Capabilities.SupportedCountries.Contains(countryCode, StringComparer.OrdinalIgnoreCase);
    }

    public bool SupportsFeature(ProviderFeature feature)
    {
        return Capabilities.SupportedFeatures.Contains(feature);
    }

    public async Task<PhoneValidationResponse> ValidatePhoneNumberAsync(string phoneNumber, string? countryCode = null)
    {
        // Basic phone number validation using regex
        var phoneRegex = new Regex(@"^\+?[1-9]\d{1,14}$");
        var isValid = phoneRegex.IsMatch(phoneNumber);

        var response = new PhoneValidationResponse
        {
            IsValid = isValid,
            FormattedNumber = phoneNumber.StartsWith('+') ? phoneNumber : $"+{phoneNumber}"
        };

        if (isValid)
        {
            response.CountryCode = GetCountryFromPhoneNumber(phoneNumber);
            response.NumberType = "mobile"; // BulkSMS primarily handles mobile numbers
        }
        else
        {
            response.Errors.Add("Invalid phone number format");
        }

        return response;
    }

    public async Task<MessageValidationResponse> ValidateMessageAsync(string message, MessageType messageType = MessageType.Text)
    {
        var response = new MessageValidationResponse
        {
            CharacterCount = message.Length,
            ContainsUnicode = message.Any(c => c > 127),
            Encoding = message.Any(c => c > 127) ? "UCS2" : "GSM7"
        };

        response.SegmentCount = CalculateSegments(message);
        response.IsValid = true;

        // Validate message length
        if (message.Length > Capabilities.MaxConcatenatedSmsLength)
        {
            response.IsValid = false;
            response.Errors.Add($"Message exceeds maximum length of {Capabilities.MaxConcatenatedSmsLength} characters");
        }

        // Check for unsupported characters in GSM7
        if (!response.ContainsUnicode)
        {
            var unsupportedChars = message.Where(c => !IsGsm7Character(c)).ToList();
            if (unsupportedChars.Any())
            {
                response.Warnings.Add($"Message contains characters that may not display correctly: {string.Join(", ", unsupportedChars.Distinct())}");
            }
        }

        return response;
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(ProviderConfiguration configuration)
    {
        var result = new ValidationResult { IsValid = true };

        if (configuration is not UsernamePasswordProviderConfiguration usernamePasswordConfig)
        {
            result.IsValid = false;
            result.Errors.Add("Configuration must be of type UsernamePasswordProviderConfiguration");
            return result;
        }

        if (string.IsNullOrEmpty(usernamePasswordConfig.Username))
        {
            result.IsValid = false;
            result.Errors.Add("Username is required");
        }

        if (string.IsNullOrEmpty(usernamePasswordConfig.Password))
        {
            result.IsValid = false;
            result.Errors.Add("Password is required");
        }

        if (usernamePasswordConfig.TimeoutSeconds < 5 || usernamePasswordConfig.TimeoutSeconds > 300)
        {
            result.Warnings.Add("Timeout should be between 5 and 300 seconds");
        }

        return result;
    }
    #endregion

    #region Health and Monitoring
    public async Task<HealthCheckResult> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            return new HealthCheckResult
            {
                IsHealthy = false,
                Message = "Provider not initialized"
            };
        }

        var stopwatch = Stopwatch.StartNew();
        try
        {
            var response = await _httpClient.GetAsync("/profile", cancellationToken);
            stopwatch.Stop();

            var isHealthy = response.IsSuccessStatusCode;
            var data = new Dictionary<string, object>
            {
                ["statusCode"] = (int)response.StatusCode,
                ["responseTime"] = stopwatch.Elapsed.TotalMilliseconds
            };

            if (isHealthy)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var profileData = JsonSerializer.Deserialize<BulkSmsProfileResponse>(responseContent, JsonOptions);

                if (profileData?.Credits != null)
                {
                    data["balance"] = profileData.Credits.Balance;
                    data["isLowBalance"] = profileData.Credits.Balance < (Limits.LowBalanceThreshold ?? 10);
                }
            }

            return new HealthCheckResult
            {
                IsHealthy = isHealthy,
                Message = isHealthy ? "Healthy" : $"HTTP {response.StatusCode}",
                ResponseTime = stopwatch.Elapsed,
                Data = data
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            return new HealthCheckResult
            {
                IsHealthy = false,
                Message = ex.Message,
                ResponseTime = stopwatch.Elapsed,
                Exception = ex.ToString()
            };
        }
    }

    public async Task<ProviderStatisticsResponse> GetStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        // BulkSMS doesn't provide detailed statistics via API, so we return basic info
        return new ProviderStatisticsResponse
        {
            ProviderName = Name,
            IsHealthy = IsAvailable,
            LastUsed = DateTime.UtcNow,
            AverageResponseTime = TimeSpan.FromMilliseconds(500), // Estimated
            PeriodStart = fromDate,
            PeriodEnd = toDate
        };
    }

    public async Task<RateLimitResponse> GetRateLimitStatusAsync(CancellationToken cancellationToken = default)
    {
        lock (_rateLimitLock)
        {
            var currentMinute = DateTime.UtcNow.ToString("yyyy-MM-dd-HH-mm");
            var used = _rateLimitTracker.GetValueOrDefault(currentMinute, 0);
            var limit = Limits.RateLimitPerMinute ?? 300;

            return new RateLimitResponse
            {
                LimitPerMinute = limit,
                Remaining = Math.Max(0, limit - used),
                ResetTime = DateTime.UtcNow.AddMinutes(1).AddSeconds(-DateTime.UtcNow.Second),
                IsExceeded = used >= limit,
                RetryAfterSeconds = used >= limit ? 60 - DateTime.UtcNow.Second : null
            };
        }
    }
    #endregion

    #region Advanced Features
    public async Task<SmsResponse> SendTemplateSmsAsync(SendTemplateSmsRequest request, CancellationToken cancellationToken = default)
    {
        // BulkSMS doesn't have native template support, so we'll process the template client-side
        // In a real implementation, you would integrate with a template engine
        var processedMessage = ProcessTemplate(request.TemplateId, request.Parameters);

        var smsRequest = new SendSmsRequest
        {
            To = request.To,
            From = request.From,
            Message = processedMessage,
            Provider = request.Provider,
            Priority = request.Priority
        };

        return await SendSmsAsync(smsRequest, cancellationToken);
    }

    public async Task<SmsResponse> SendMmsAsync(SendMmsRequest request, CancellationToken cancellationToken = default)
    {
        // BulkSMS doesn't support MMS, return error
        return new SmsResponse
        {
            Success = false,
            Status = SmsStatus.Failed,
            ErrorMessage = "MMS is not supported by BulkSMS provider",
            ErrorCode = "MMS_NOT_SUPPORTED",
            Provider = Name,
            Timestamp = DateTime.UtcNow
        };
    }

    public async Task<InboxResponse> GetInboxAsync(GetInboxRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var url = $"/messages/inbox?limit={request.PageSize}&offset={(request.Page - 1) * request.PageSize}";

            if (request.FromDate.HasValue)
                url += $"&from={request.FromDate.Value:yyyy-MM-ddTHH:mm:ssZ}";

            if (request.ToDate.HasValue)
                url += $"&to={request.ToDate.Value:yyyy-MM-ddTHH:mm:ssZ}";

            var response = await _httpClient.GetAsync(url, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var inboxData = JsonSerializer.Deserialize<BulkSmsInboxResponse>(responseContent, JsonOptions);

                return new InboxResponse
                {
                    Messages = inboxData?.Messages?.Select(m => new InboxMessage
                    {
                        Id = m.Id ?? "",
                        From = m.From ?? "",
                        To = m.To ?? "",
                        Message = m.Body ?? "",
                        ReceivedAt = m.ReceivedDate ?? DateTime.UtcNow,
                        IsRead = false, // BulkSMS doesn't track read status
                        MessageType = MessageType.Text
                    }).ToList() ?? new List<InboxMessage>(),
                    TotalCount = inboxData?.TotalCount ?? 0,
                    Page = request.Page,
                    PageSize = request.PageSize,
                    HasMore = (inboxData?.TotalCount ?? 0) > request.Page * request.PageSize
                };
            }

            return new InboxResponse
            {
                Messages = new List<InboxMessage>(),
                TotalCount = 0,
                Page = request.Page,
                PageSize = request.PageSize,
                HasMore = false
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to get inbox messages");
            return new InboxResponse
            {
                Messages = new List<InboxMessage>(),
                TotalCount = 0,
                Page = request.Page,
                PageSize = request.PageSize,
                HasMore = false
            };
        }
    }

    public async Task<WebhookConfigResponse> ConfigureWebhookAsync(string webhookUrl, List<WebhookEvent> events, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var webhookConfig = new
            {
                url = webhookUrl,
                events = events.Select(e => e.ToString().ToLowerInvariant()).ToArray()
            };

            var jsonContent = JsonSerializer.Serialize(webhookConfig, JsonOptions);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/webhooks", content, cancellationToken);

            return new WebhookConfigResponse
            {
                Success = response.IsSuccessStatusCode,
                WebhookUrl = webhookUrl,
                Events = events,
                ErrorMessage = response.IsSuccessStatusCode ? null : $"HTTP {response.StatusCode}"
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to configure webhook");
            return new WebhookConfigResponse
            {
                Success = false,
                WebhookUrl = webhookUrl,
                Events = events,
                ErrorMessage = ex.Message
            };
        }
    }
    #endregion

    #region Helper Methods
    private string GetCountryFromPhoneNumber(string phoneNumber)
    {
        // Simple country detection based on phone number prefix
        var cleanNumber = phoneNumber.TrimStart('+');

        return cleanNumber switch
        {
            var n when n.StartsWith("1") => "US",
            var n when n.StartsWith("44") => "GB",
            var n when n.StartsWith("27") => "ZA",
            var n when n.StartsWith("49") => "DE",
            var n when n.StartsWith("33") => "FR",
            var n when n.StartsWith("34") => "ES",
            var n when n.StartsWith("39") => "IT",
            var n when n.StartsWith("31") => "NL",
            var n when n.StartsWith("32") => "BE",
            var n when n.StartsWith("41") => "CH",
            var n when n.StartsWith("43") => "AT",
            var n when n.StartsWith("46") => "SE",
            var n when n.StartsWith("47") => "NO",
            var n when n.StartsWith("45") => "DK",
            var n when n.StartsWith("358") => "FI",
            var n when n.StartsWith("61") => "AU",
            _ => "UNKNOWN"
        };
    }

    private bool IsGsm7Character(char c)
    {
        // GSM 7-bit character set validation
        var gsm7Chars = "@£$¥èéùìòÇ\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà";
        return gsm7Chars.Contains(c);
    }

    private string ProcessTemplate(string templateId, Dictionary<string, object> parameters)
    {
        // Simple template processing - in a real implementation, use a proper template engine
        var template = GetTemplateContent(templateId);

        foreach (var param in parameters)
        {
            template = template.Replace($"{{{param.Key}}}", param.Value?.ToString() ?? "");
        }

        return template;
    }

    private string GetTemplateContent(string templateId)
    {
        // Mock template content - in a real implementation, fetch from template store
        return templateId switch
        {
            "welcome" => "Welcome {name}! Your account has been created successfully.",
            "verification" => "Your verification code is {code}. Valid for {minutes} minutes.",
            "reminder" => "Hi {name}, this is a reminder about {event} on {date}.",
            _ => "Template not found: {templateId}"
        };
    }

    public async ValueTask DisposeAsync()
    {
        _httpClient?.Dispose();
        GC.SuppressFinalize(this);
    }
    #endregion
}
