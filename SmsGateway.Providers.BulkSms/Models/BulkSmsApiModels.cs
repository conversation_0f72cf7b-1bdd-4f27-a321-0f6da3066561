using System.Text.Json.Serialization;

namespace SmsGateway.Providers.BulkSms.Models;

/// <summary>
/// BulkSMS API request model
/// </summary>
public class BulkSmsApiRequest
{
    [JsonPropertyName("to")]
    public string To { get; set; } = string.Empty;

    [JsonPropertyName("from")]
    public string From { get; set; } = string.Empty;

    [JsonPropertyName("body")]
    public string Body { get; set; } = string.Empty;

    [JsonPropertyName("deliveryReports")]
    public string? DeliveryReports { get; set; }

    [JsonPropertyName("protocolId")]
    public int? ProtocolId { get; set; }

    [JsonPropertyName("encoding")]
    public string? Encoding { get; set; }

    [JsonPropertyName("messageClass")]
    public int? MessageClass { get; set; }

    [JsonPropertyName("userSuppliedId")]
    public string? UserSuppliedId { get; set; }

    [JsonPropertyName("sendTime")]
    public string? SendTime { get; set; }

    [JsonPropertyName("validityPeriod")]
    public int? ValidityPeriod { get; set; }
}

/// <summary>
/// BulkSMS API response model
/// </summary>
public class BulkSmsApiResponse
{
    [JsonPropertyName("id")]
    public string? Id { get; set; }

    [JsonPropertyName("type")]
    public string? Type { get; set; }

    [JsonPropertyName("from")]
    public string? From { get; set; }

    [JsonPropertyName("to")]
    public string? To { get; set; }

    [JsonPropertyName("body")]
    public string? Body { get; set; }

    [JsonPropertyName("encoding")]
    public string? Encoding { get; set; }

    [JsonPropertyName("protocolId")]
    public int? ProtocolId { get; set; }

    [JsonPropertyName("messageClass")]
    public int? MessageClass { get; set; }

    [JsonPropertyName("numberOfParts")]
    public int? NumberOfParts { get; set; }

    [JsonPropertyName("creditCost")]
    public decimal? CreditCost { get; set; }

    [JsonPropertyName("submissionDate")]
    public DateTime? SubmissionDate { get; set; }

    [JsonPropertyName("status")]
    public BulkSmsStatus? Status { get; set; }

    [JsonPropertyName("userSuppliedId")]
    public string? UserSuppliedId { get; set; }

    [JsonPropertyName("relatedSentMessageId")]
    public string? RelatedSentMessageId { get; set; }
}

/// <summary>
/// BulkSMS status model
/// </summary>
public class BulkSmsStatus
{
    [JsonPropertyName("type")]
    public string? Type { get; set; }

    [JsonPropertyName("subtype")]
    public string? Subtype { get; set; }

    [JsonPropertyName("description")]
    public string? Description { get; set; }

    [JsonPropertyName("detail")]
    public string? Detail { get; set; }
}

/// <summary>
/// BulkSMS error response model
/// </summary>
public class BulkSmsErrorResponse
{
    [JsonPropertyName("type")]
    public string? Type { get; set; }

    [JsonPropertyName("title")]
    public string? Title { get; set; }

    [JsonPropertyName("detail")]
    public string? Detail { get; set; }

    [JsonPropertyName("status")]
    public int? Status { get; set; }

    [JsonPropertyName("instance")]
    public string? Instance { get; set; }
}

/// <summary>
/// BulkSMS pricing response model
/// </summary>
public class BulkSmsPricingResponse
{
    [JsonPropertyName("countryCode")]
    public string? CountryCode { get; set; }

    [JsonPropertyName("countryName")]
    public string? CountryName { get; set; }

    [JsonPropertyName("price")]
    public decimal? Price { get; set; }

    [JsonPropertyName("currency")]
    public string? Currency { get; set; }

    [JsonPropertyName("networks")]
    public List<BulkSmsNetworkPricing>? Networks { get; set; }
}

/// <summary>
/// BulkSMS network pricing model
/// </summary>
public class BulkSmsNetworkPricing
{
    [JsonPropertyName("network")]
    public string? Network { get; set; }

    [JsonPropertyName("price")]
    public decimal? Price { get; set; }
}

/// <summary>
/// BulkSMS profile response model
/// </summary>
public class BulkSmsProfileResponse
{
    [JsonPropertyName("id")]
    public string? Id { get; set; }

    [JsonPropertyName("username")]
    public string? Username { get; set; }

    [JsonPropertyName("credits")]
    public BulkSmsCredits? Credits { get; set; }

    [JsonPropertyName("quota")]
    public BulkSmsQuota? Quota { get; set; }
}

/// <summary>
/// BulkSMS credits model
/// </summary>
public class BulkSmsCredits
{
    [JsonPropertyName("balance")]
    public decimal Balance { get; set; }

    [JsonPropertyName("isTransferAllowed")]
    public bool IsTransferAllowed { get; set; }
}

/// <summary>
/// BulkSMS quota model
/// </summary>
public class BulkSmsQuota
{
    [JsonPropertyName("size")]
    public int Size { get; set; }

    [JsonPropertyName("remaining")]
    public int Remaining { get; set; }
}

/// <summary>
/// BulkSMS inbox response model
/// </summary>
public class BulkSmsInboxResponse
{
    [JsonPropertyName("messages")]
    public List<BulkSmsInboxMessage>? Messages { get; set; }

    [JsonPropertyName("totalCount")]
    public int TotalCount { get; set; }
}

/// <summary>
/// BulkSMS inbox message model
/// </summary>
public class BulkSmsInboxMessage
{
    [JsonPropertyName("id")]
    public string? Id { get; set; }

    [JsonPropertyName("type")]
    public string? Type { get; set; }

    [JsonPropertyName("from")]
    public string? From { get; set; }

    [JsonPropertyName("to")]
    public string? To { get; set; }

    [JsonPropertyName("body")]
    public string? Body { get; set; }

    [JsonPropertyName("encoding")]
    public string? Encoding { get; set; }

    [JsonPropertyName("receivedDate")]
    public DateTime? ReceivedDate { get; set; }

    [JsonPropertyName("messageClass")]
    public int? MessageClass { get; set; }
}

/// <summary>
/// BulkSMS webhook configuration model
/// </summary>
public class BulkSmsWebhookConfig
{
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("contactEmailAddress")]
    public string? ContactEmailAddress { get; set; }

    [JsonPropertyName("isActive")]
    public bool IsActive { get; set; } = true;

    [JsonPropertyName("triggerScope")]
    public string? TriggerScope { get; set; }
}

/// <summary>
/// BulkSMS delivery report webhook model
/// </summary>
public class BulkSmsDeliveryReport
{
    [JsonPropertyName("id")]
    public string? Id { get; set; }

    [JsonPropertyName("type")]
    public string? Type { get; set; }

    [JsonPropertyName("status")]
    public BulkSmsStatus? Status { get; set; }

    [JsonPropertyName("phoneNumber")]
    public string? PhoneNumber { get; set; }

    [JsonPropertyName("sentDate")]
    public DateTime? SentDate { get; set; }

    [JsonPropertyName("receivedDate")]
    public DateTime? ReceivedDate { get; set; }

    [JsonPropertyName("userSuppliedId")]
    public string? UserSuppliedId { get; set; }
}
