using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models;
using SmsGateway.Core.Models.Common;

namespace SmsGateway.Providers.BulkSms;

/// <summary>
/// Plugin factory for BulkSMS provider
/// </summary>
public class BulkSmsPluginFactory : PluginFactoryBase
{
    public override PluginMetadata Metadata => new()
    {
        AssemblyName = "SmsGateway.Providers.BulkSms",
        Version = "1.0.0",
        Author = "SMS Gateway Team",
        Description = "BulkSMS provider for sending SMS via BulkSMS API",
        Tags = { "bulksms", "sms", "production", "international" },
        MinimumCoreVersion = "1.0.0",
        Dependencies = { "System.Text.Json" }
    };

    public override IEnumerable<ISmsProvider> CreateProviders()
    {
        // Create HTTP client and logger if available (will be null in plugin context)
        var httpClient = new HttpClient();
        ILogger<BulkSmsProvider>? logger = null;
        
        yield return new BulkSmsProvider(httpClient, logger);
    }

    public override IEnumerable<Type> GetSupportedConfigurationTypes()
    {
        return new[] { typeof(UsernamePasswordProviderConfiguration) };
    }

    public override ValidationResult ValidateDependencies()
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // Check if required types are available
            var httpClientType = typeof(HttpClient);
            var jsonSerializerType = typeof(System.Text.Json.JsonSerializer);
            
            if (httpClientType == null || jsonSerializerType == null)
            {
                result.IsValid = false;
                result.Errors.Add("Required dependencies not available");
            }
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Dependency validation failed: {ex.Message}");
        }

        return result;
    }
}
