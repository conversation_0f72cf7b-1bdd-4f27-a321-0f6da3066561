using System.Diagnostics;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Common;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;

namespace SmsGateway.Providers.BulkSms;

/// <summary>
/// Enhanced BulkSMS provider implementation with comprehensive features
/// </summary>
public class EnhancedBulkSmsProvider : ISmsProvider
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<EnhancedBulkSmsProvider>? _logger;
    private UsernamePasswordProviderConfiguration? _configuration;
    private readonly Dictionary<string, int> _rateLimitTracker = new();
    private readonly object _rateLimitLock = new();

    #region Basic Properties
    public string Name => "BulkSMS";
    public string DisplayName => "BulkSMS Enhanced";
    public string Version => "2.0.0";
    public string Description => "Enhanced BulkSMS provider with comprehensive SMS features including delivery reports, scheduling, MMS, and advanced analytics";
    public string Author => "SMS Gateway Team";
    public string? WebsiteUrl => "https://www.bulksms.com";
    public string? DocumentationUrl => "https://developer.bulksms.com/json/v1/";
    public bool IsAvailable => _configuration != null && !string.IsNullOrEmpty(_configuration.Username) && !string.IsNullOrEmpty(_configuration.Password);

    public ProviderCapabilities Capabilities => new()
    {
        SupportedFeatures = new List<ProviderFeature>
        {
            ProviderFeature.BasicSms,
            ProviderFeature.BulkSms,
            ProviderFeature.ScheduledSms,
            ProviderFeature.DeliveryReports,
            ProviderFeature.Unicode,
            ProviderFeature.LongMessages,
            ProviderFeature.AlphanumericSenderId,
            ProviderFeature.CustomSenderId,
            ProviderFeature.Webhooks,
            ProviderFeature.RealTimeStatus,
            ProviderFeature.Analytics,
            ProviderFeature.OptOutManagement
        },
        SupportedMessageTypes = new List<MessageType>
        {
            MessageType.Text,
            MessageType.Unicode,
            MessageType.Flash
        },
        SupportedCountries = new List<string>
        {
            "US", "CA", "GB", "AU", "ZA", "DE", "FR", "ES", "IT", "NL", "BE", "CH", "AT", "SE", "NO", "DK", "FI"
        },
        SupportedCharsets = new List<string> { "UTF-8", "GSM7", "UCS2" },
        MaxSingleSmsLength = 160,
        MaxUnicodeSmsLength = 70,
        MaxConcatenatedSmsLength = 1600,
        MaxConcatenatedParts = 10,
        SupportsCustomSenderId = true,
        SupportsAlphanumericSenderId = true,
        MaxSenderIdLength = 11,
        SupportsDeliveryReports = true,
        SupportsScheduledSending = true,
        MaxSchedulingHours = 24 * 365, // 1 year
        SupportsBulkSending = true,
        MaxBulkSize = 50000,
        SupportsTwoWay = true,
        SupportsMms = false
    };

    public ProviderLimits Limits => new()
    {
        RateLimitPerSecond = 10,
        RateLimitPerMinute = 300,
        RateLimitPerHour = 10000,
        RateLimitPerDay = 100000,
        MaxConcurrentConnections = 10,
        RequestTimeoutSeconds = 30,
        MaxRetryAttempts = 3,
        RetryDelaySeconds = 5,
        MinimumBalance = 1.0m,
        LowBalanceThreshold = 10.0m
    };
    #endregion

    public EnhancedBulkSmsProvider(HttpClient? httpClient = null, ILogger<EnhancedBulkSmsProvider>? logger = null)
    {
        _httpClient = httpClient ?? new HttpClient();
        _logger = logger;
    }

    #region Core SMS Operations
    public async Task InitializeAsync(ProviderConfiguration configuration)
    {
        if (configuration is not UsernamePasswordProviderConfiguration usernamePasswordConfig)
        {
            throw new ArgumentException("BulkSMS provider requires UsernamePasswordProviderConfiguration");
        }

        _configuration = usernamePasswordConfig;
        
        // Set default base URL if not provided
        if (string.IsNullOrEmpty(_configuration.BaseUrl))
        {
            _configuration.BaseUrl = "https://api.bulksms.com/v1";
        }

        // Configure HTTP client
        _httpClient.BaseAddress = new Uri(_configuration.BaseUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(_configuration.TimeoutSeconds);
        
        // Set up basic authentication
        var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_configuration.Username}:{_configuration.Password}"));
        _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authValue);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "SmsGateway-Enhanced/2.0");

        _logger?.LogInformation("Enhanced BulkSMS provider initialized with advanced features");
    }

    public async Task<SmsResponse> SendSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        await CheckRateLimitAsync();

        try
        {
            var bulkSmsRequest = new BulkSmsApiRequest
            {
                To = request.To,
                From = request.From ?? _configuration.DefaultSenderId ?? "",
                Body = request.Message,
                DeliveryReports = "ALL",
                ProtocolId = request.Priority == SmsPriority.Critical ? 1 : 0,
                UserSuppliedId = request.Reference,
                SendTime = request.ScheduledAt?.ToString("yyyy-MM-ddTHH:mm:ssZ")
            };

            var jsonContent = JsonSerializer.Serialize(new[] { bulkSmsRequest }, JsonOptions);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/messages", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var bulkSmsResponses = JsonSerializer.Deserialize<BulkSmsApiResponse[]>(responseContent, JsonOptions);
                var bulkSmsResponse = bulkSmsResponses?.FirstOrDefault();
                
                if (bulkSmsResponse != null)
                {
                    return new SmsResponse
                    {
                        Success = true,
                        MessageId = bulkSmsResponse.Id,
                        Status = MapBulkSmsStatus(bulkSmsResponse.Status?.Type),
                        Cost = bulkSmsResponse.CreditCost,
                        Currency = "Credits",
                        Segments = CalculateSegments(request.Message),
                        Provider = Name,
                        Timestamp = DateTime.UtcNow,
                        Metadata = new Dictionary<string, object>
                        {
                            ["encoding"] = bulkSmsResponse.Encoding ?? "GSM7",
                            ["messageClass"] = bulkSmsResponse.MessageClass ?? 1,
                            ["protocolId"] = bulkSmsResponse.ProtocolId ?? 0
                        }
                    };
                }
            }

            // Handle error response
            var errorResponse = await ParseErrorResponseAsync(responseContent);
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = errorResponse.Detail ?? $"HTTP {response.StatusCode}",
                ErrorCode = errorResponse.Type ?? response.StatusCode.ToString(),
                Provider = Name,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to send SMS via BulkSMS");
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "BULKSMS_EXCEPTION",
                Provider = Name,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<BulkSmsResponse> SendBulkSmsAsync(SendBulkSmsRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        var responses = new List<SmsResponse>();
        var batchSize = Math.Min(request.Messages.Count, Capabilities.MaxBulkSize);
        
        for (int i = 0; i < request.Messages.Count; i += batchSize)
        {
            var batch = request.Messages.Skip(i).Take(batchSize).ToList();
            var batchRequests = batch.Select(msg => new BulkSmsApiRequest
            {
                To = msg.To,
                From = msg.From ?? request.DefaultFrom ?? _configuration.DefaultSenderId ?? "",
                Body = msg.Message,
                DeliveryReports = "ALL",
                ProtocolId = msg.Priority == SmsPriority.Critical ? 1 : 0,
                UserSuppliedId = msg.Reference,
                SendTime = msg.ScheduledAt?.ToString("yyyy-MM-ddTHH:mm:ssZ")
            }).ToArray();

            try
            {
                await CheckRateLimitAsync();
                
                var jsonContent = JsonSerializer.Serialize(batchRequests, JsonOptions);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync("/messages", content, cancellationToken);
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var bulkSmsResponses = JsonSerializer.Deserialize<BulkSmsApiResponse[]>(responseContent, JsonOptions);
                    
                    if (bulkSmsResponses != null)
                    {
                        for (int j = 0; j < bulkSmsResponses.Length && j < batch.Count; j++)
                        {
                            var bulkSmsResponse = bulkSmsResponses[j];
                            var originalRequest = batch[j];
                            
                            responses.Add(new SmsResponse
                            {
                                Success = true,
                                MessageId = bulkSmsResponse.Id,
                                Status = MapBulkSmsStatus(bulkSmsResponse.Status?.Type),
                                Cost = bulkSmsResponse.CreditCost,
                                Currency = "Credits",
                                Segments = CalculateSegments(originalRequest.Message),
                                Provider = Name,
                                Timestamp = DateTime.UtcNow
                            });
                        }
                    }
                }
                else
                {
                    // Handle batch error
                    var errorResponse = await ParseErrorResponseAsync(responseContent);
                    foreach (var msg in batch)
                    {
                        responses.Add(new SmsResponse
                        {
                            Success = false,
                            Status = SmsStatus.Failed,
                            ErrorMessage = errorResponse.Detail ?? $"HTTP {response.StatusCode}",
                            ErrorCode = errorResponse.Type ?? response.StatusCode.ToString(),
                            Provider = Name,
                            Timestamp = DateTime.UtcNow
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to send bulk SMS batch via BulkSMS");
                foreach (var msg in batch)
                {
                    responses.Add(new SmsResponse
                    {
                        Success = false,
                        Status = SmsStatus.Failed,
                        ErrorMessage = ex.Message,
                        ErrorCode = "BULKSMS_BATCH_EXCEPTION",
                        Provider = Name,
                        Timestamp = DateTime.UtcNow
                    });
                }
            }
        }

        var successfulCount = responses.Count(r => r.Success);
        var totalCost = responses.Where(r => r.Cost.HasValue).Sum(r => r.Cost.Value);

        return new BulkSmsResponse
        {
            Success = successfulCount > 0,
            TotalMessages = responses.Count,
            SuccessfulMessages = successfulCount,
            FailedMessages = responses.Count - successfulCount,
            MessageResponses = responses,
            TotalCost = totalCost,
            Currency = "Credits",
            Timestamp = DateTime.UtcNow
        };
    }

    public async Task<SmsResponse> ScheduleSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default)
    {
        if (!request.ScheduledAt.HasValue)
        {
            request.ScheduledAt = DateTime.UtcNow.AddMinutes(5); // Default to 5 minutes from now
        }

        return await SendSmsAsync(request, cancellationToken);
    }

    public async Task<bool> CancelScheduledSmsAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        try
        {
            var response = await _httpClient.DeleteAsync($"/messages/{messageId}", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to cancel scheduled SMS {MessageId}", messageId);
            return false;
        }
    }
    #endregion

    #region Helper Methods
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true
    };

    private async Task CheckRateLimitAsync()
    {
        lock (_rateLimitLock)
        {
            var currentMinute = DateTime.UtcNow.ToString("yyyy-MM-dd-HH-mm");
            if (!_rateLimitTracker.ContainsKey(currentMinute))
            {
                _rateLimitTracker.Clear();
                _rateLimitTracker[currentMinute] = 0;
            }

            if (_rateLimitTracker[currentMinute] >= (Limits.RateLimitPerMinute ?? 300))
            {
                throw new InvalidOperationException("Rate limit exceeded");
            }

            _rateLimitTracker[currentMinute]++;
        }
    }

    private SmsStatus MapBulkSmsStatus(string? bulkSmsStatus)
    {
        return bulkSmsStatus?.ToUpperInvariant() switch
        {
            "ACCEPTED" => SmsStatus.Queued,
            "SCHEDULED" => SmsStatus.Scheduled,
            "SENT" => SmsStatus.Sent,
            "DELIVERED" => SmsStatus.Delivered,
            "UNKNOWN" => SmsStatus.Unknown,
            "REJECTED" => SmsStatus.Rejected,
            "FAILED" => SmsStatus.Failed,
            "EXPIRED" => SmsStatus.Failed,
            _ => SmsStatus.Unknown
        };
    }

    private int CalculateSegments(string message)
    {
        var containsUnicode = message.Any(c => c > 127);
        var maxLength = containsUnicode ? 70 : 160;
        return (int)Math.Ceiling((double)message.Length / maxLength);
    }

    private async Task<BulkSmsErrorResponse> ParseErrorResponseAsync(string responseContent)
    {
        try
        {
            return JsonSerializer.Deserialize<BulkSmsErrorResponse>(responseContent, JsonOptions) ?? new BulkSmsErrorResponse();
        }
        catch
        {
            return new BulkSmsErrorResponse { Detail = responseContent };
        }
    }
    #endregion
}
