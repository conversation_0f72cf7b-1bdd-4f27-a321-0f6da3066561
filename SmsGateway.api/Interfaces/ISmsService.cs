using SmsGateway.api.Models;

namespace SmsGateway.api.Interfaces;

/// <summary>
/// Main service interface for SMS operations
/// </summary>
public interface ISmsService
{
    /// <summary>
    /// Send an SMS message using the best available provider
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response with delivery details</returns>
    Task<SmsResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send an SMS message using a specific provider
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="providerName">Name of the provider to use</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response with delivery details</returns>
    Task<SmsResponse> SendSmsAsync(SmsRequest request, string providerName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send multiple SMS messages in batch
    /// </summary>
    /// <param name="requests">Collection of SMS requests</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of SMS responses</returns>
    Task<IEnumerable<SmsResponse>> SendBatchSmsAsync(IEnumerable<SmsRequest> requests, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get the status of a previously sent message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="providerName">Provider that sent the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current message status</returns>
    Task<SmsStatus> GetMessageStatusAsync(string messageId, string providerName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get estimated cost for sending an SMS
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="providerName">Optional specific provider name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Estimated cost and currency</returns>
    Task<(decimal cost, string currency)?> GetEstimatedCostAsync(SmsRequest request, string? providerName = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all available providers
    /// </summary>
    /// <returns>Collection of provider information</returns>
    Task<IEnumerable<ProviderInfo>> GetAvailableProvidersAsync();

    /// <summary>
    /// Get provider statistics
    /// </summary>
    /// <param name="providerName">Optional provider name (if null, returns all)</param>
    /// <returns>Provider statistics</returns>
    Task<IEnumerable<ProviderStatistics>> GetProviderStatisticsAsync(string? providerName = null);

    /// <summary>
    /// Perform health check on all providers
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check results for all providers</returns>
    Task<IEnumerable<ProviderHealthStatus>> HealthCheckAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Information about a provider
/// </summary>
public class ProviderInfo
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public bool IsAvailable { get; set; }
    public int Priority { get; set; }
    public List<string> SupportedCountries { get; set; } = new();
}

/// <summary>
/// Health status of a provider
/// </summary>
public class ProviderHealthStatus
{
    public string ProviderName { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public string? Message { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object>? Data { get; set; }
}
