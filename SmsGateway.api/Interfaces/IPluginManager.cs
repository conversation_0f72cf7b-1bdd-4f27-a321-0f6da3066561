using SmsGateway.api.Models;

namespace SmsGateway.api.Interfaces;

/// <summary>
/// Interface for managing SMS provider plugins
/// </summary>
public interface IPluginManager
{
    /// <summary>
    /// Load all available plugins from the plugins directory
    /// </summary>
    Task LoadPluginsAsync();

    /// <summary>
    /// Register a provider plugin
    /// </summary>
    /// <param name="provider">Provider instance to register</param>
    /// <param name="configuration">Provider configuration</param>
    Task RegisterProviderAsync(ISmsProvider provider, ProviderConfiguration configuration);

    /// <summary>
    /// Unregister a provider plugin
    /// </summary>
    /// <param name="providerName">Name of the provider to unregister</param>
    Task UnregisterProviderAsync(string providerName);

    /// <summary>
    /// Get a specific provider by name
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Provider instance or null if not found</returns>
    ISmsProvider? GetProvider(string providerName);

    /// <summary>
    /// Get all registered providers
    /// </summary>
    /// <returns>Collection of all registered providers</returns>
    IEnumerable<ISmsProvider> GetAllProviders();

    /// <summary>
    /// Get all enabled providers ordered by priority
    /// </summary>
    /// <returns>Collection of enabled providers in priority order</returns>
    IEnumerable<ISmsProvider> GetEnabledProviders();

    /// <summary>
    /// Get the best provider for a specific request
    /// </summary>
    /// <param name="request">SMS request to find provider for</param>
    /// <returns>Best matching provider or null if none available</returns>
    ISmsProvider? GetBestProvider(SmsRequest request);

    /// <summary>
    /// Get provider configuration
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Provider configuration or null if not found</returns>
    ProviderConfiguration? GetProviderConfiguration(string providerName);

    /// <summary>
    /// Update provider configuration
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <param name="configuration">New configuration</param>
    Task UpdateProviderConfigurationAsync(string providerName, ProviderConfiguration configuration);

    /// <summary>
    /// Check if a provider is registered and enabled
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>True if provider is registered and enabled</returns>
    bool IsProviderEnabled(string providerName);

    /// <summary>
    /// Get provider statistics
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Provider statistics or null if not found</returns>
    ProviderStatistics? GetProviderStatistics(string providerName);
}

/// <summary>
/// Statistics for a provider
/// </summary>
public class ProviderStatistics
{
    public string ProviderName { get; set; } = string.Empty;
    public int TotalMessagesSent { get; set; }
    public int SuccessfulMessages { get; set; }
    public int FailedMessages { get; set; }
    public decimal SuccessRate => TotalMessagesSent > 0 ? (decimal)SuccessfulMessages / TotalMessagesSent * 100 : 0;
    public TimeSpan AverageResponseTime { get; set; }
    public DateTime LastUsed { get; set; }
    public bool IsHealthy { get; set; }
    public string? LastError { get; set; }
}
