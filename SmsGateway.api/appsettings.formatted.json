{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "SmsProviders": {"Mock": {"Type": "basic", "Name": "<PERSON><PERSON>", "DisplayName": "Mock SMS Provider", "Enabled": true, "Priority": 999, "MaxRetries": 3, "TimeoutSeconds": 30, "DefaultSenderId": "MockSMS", "SupportedCountries": [], "Settings": {}}, "Twilio": {"Type": "apikey", "Name": "<PERSON><PERSON><PERSON>", "DisplayName": "<PERSON><PERSON><PERSON>", "Enabled": false, "Priority": 1, "MaxRetries": 3, "TimeoutSeconds": 30, "ApiKey": "your_twilio_auth_token_here", "BaseUrl": "https://api.twilio.com/2010-04-01", "DefaultSenderId": "+**********", "SupportedCountries": [], "Settings": {"AccountSid": "your_twilio_account_sid_here"}}}}