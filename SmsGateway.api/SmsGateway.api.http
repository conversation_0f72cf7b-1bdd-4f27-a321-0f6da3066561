@SmsGateway.api_HostAddress = http://localhost:5270

### Send SMS using best available provider
POST {{SmsGateway.api_HostAddress}}/api/sms/send
Content-Type: application/json

{
  "to": "+**********",
  "from": "SmsGateway",
  "message": "Hello from SMS Gateway! This is a test message.",
  "priority": 1
}

### Send SMS using specific provider (Mock)
POST {{SmsGateway.api_HostAddress}}/api/sms/send/Mock
Content-Type: application/json

{
  "to": "+**********",
  "message": "Hello from Mock provider!"
}

### Send batch SMS
POST {{SmsGateway.api_HostAddress}}/api/sms/send/batch
Content-Type: application/json

[
  {
    "to": "+**********",
    "message": "Batch message 1"
  },
  {
    "to": "+**********",
    "message": "Batch message 2"
  }
]

### Get message status
GET {{SmsGateway.api_HostAddress}}/api/sms/status/your-message-id-here?provider=Mock
Accept: application/json

### Get cost estimate
POST {{SmsGateway.api_HostAddress}}/api/sms/cost/estimate
Content-Type: application/json

{
  "to": "+**********",
  "message": "Cost estimation test message"
}

### Get all providers
GET {{SmsGateway.api_HostAddress}}/api/providers
Accept: application/json

### Get specific provider info
GET {{SmsGateway.api_HostAddress}}/api/providers/Mock
Accept: application/json

### Get provider statistics
GET {{SmsGateway.api_HostAddress}}/api/providers/statistics
Accept: application/json

### Get provider health status
GET {{SmsGateway.api_HostAddress}}/api/providers/health
Accept: application/json

### Get enabled providers
GET {{SmsGateway.api_HostAddress}}/api/providers/enabled
Accept: application/json
