namespace SmsGateway.api.Models;

/// <summary>
/// Base configuration for SMS providers
/// </summary>
public abstract class ProviderConfiguration
{
    /// <summary>
    /// Unique name/identifier for the provider
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Display name for the provider
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether this provider is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Priority order (lower numbers = higher priority)
    /// </summary>
    public int Priority { get; set; } = 100;

    /// <summary>
    /// Maximum retry attempts for failed messages
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Timeout in seconds for API calls
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Rate limiting: maximum messages per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Default sender ID for this provider
    /// </summary>
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Supported countries (ISO 3166-1 alpha-2 codes). Empty means all countries.
    /// </summary>
    public List<string> SupportedCountries { get; set; } = new();

    /// <summary>
    /// Provider-specific settings
    /// </summary>
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Configuration for providers that use API keys
/// </summary>
public class ApiKeyProviderConfiguration : ProviderConfiguration
{
    /// <summary>
    /// API key for authentication
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// Base URL for the provider's API
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;
}

/// <summary>
/// Configuration for providers that use username/password authentication
/// </summary>
public class UsernamePasswordProviderConfiguration : ProviderConfiguration
{
    /// <summary>
    /// Username for authentication
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password for authentication
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Base URL for the provider's API
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;
}
