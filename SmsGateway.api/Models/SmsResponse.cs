namespace SmsGateway.api.Models;

/// <summary>
/// Represents the response from an SMS sending operation
/// </summary>
public class SmsResponse
{
    /// <summary>
    /// Indicates if the SMS was successfully sent
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Unique message ID from the provider
    /// </summary>
    public string? MessageId { get; set; }

    /// <summary>
    /// Provider that handled the request
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the message
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Error message if sending failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if sending failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Timestamp when the response was generated
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Cost of sending the message (if available)
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Currency of the cost
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// Number of message segments used
    /// </summary>
    public int? Segments { get; set; }

    /// <summary>
    /// Additional metadata from the provider
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// SMS delivery status
/// </summary>
public enum SmsStatus
{
    Pending = 0,
    Queued = 1,
    Sent = 2,
    Delivered = 3,
    Failed = 4,
    Undelivered = 5,
    Unknown = 6
}
