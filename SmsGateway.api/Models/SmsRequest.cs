using System.ComponentModel.DataAnnotations;

namespace SmsGateway.api.Models;

/// <summary>
/// Represents an SMS sending request
/// </summary>
public class SmsRequest
{
    /// <summary>
    /// Recipient phone number (E.164 format recommended)
    /// </summary>
    [Required]
    [Phone]
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender phone number or alphanumeric sender ID
    /// </summary>
    public string? From { get; set; }

    /// <summary>
    /// SMS message content
    /// </summary>
    [Required]
    [StringLength(1600, MinimumLength = 1)]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Optional provider to use for sending (if not specified, uses default)
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Optional callback URL for delivery status updates
    /// </summary>
    [Url]
    public string? CallbackUrl { get; set; }

    /// <summary>
    /// Optional reference ID for tracking
    /// </summary>
    public string? Reference { get; set; }

    /// <summary>
    /// Priority level for the message
    /// </summary>
    public SmsPriority Priority { get; set; } = SmsPriority.Normal;

    /// <summary>
    /// Optional scheduled send time (UTC)
    /// </summary>
    public DateTime? ScheduledAt { get; set; }
}

/// <summary>
/// SMS priority levels
/// </summary>
public enum SmsPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}
