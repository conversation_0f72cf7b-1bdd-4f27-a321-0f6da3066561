using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Core.Models.Common;
using System.ComponentModel.DataAnnotations;

namespace SmsGateway.Api.Controllers;

/// <summary>
/// Enhanced SMS API controller with comprehensive features
/// </summary>
[ApiController]
[Route("api/v2/[controller]")]
[Authorize]
[Produces("application/json")]
public class SmsController : ControllerBase
{
    private readonly ISmsService _smsService;
    private readonly ILogger<SmsController> _logger;

    public SmsController(ISmsService smsService, ILogger<SmsController> logger)
    {
        _smsService = smsService;
        _logger = logger;
    }

    /// <summary>
    /// Send a single SMS message
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response with delivery details</returns>
    [HttpPost("send")]
    [ProducesResponseType(typeof(SmsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<SmsResponse>> SendSms([FromBody] SendSmsRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.SendSmsAsync(request, cancellationToken);
            
            if (response.Success)
            {
                return Ok(response);
            }
            
            return BadRequest(new { error = response.ErrorMessage, code = response.ErrorCode });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Send multiple SMS messages in a batch
    /// </summary>
    /// <param name="request">Bulk SMS request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk SMS response</returns>
    [HttpPost("send/batch")]
    [ProducesResponseType(typeof(BulkSmsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<BulkSmsResponse>> SendBulkSms([FromBody] SendBulkSmsRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.SendBulkSmsAsync(request, cancellationToken);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send bulk SMS");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Schedule an SMS message for future delivery
    /// </summary>
    /// <param name="request">SMS request with scheduled time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response</returns>
    [HttpPost("schedule")]
    [ProducesResponseType(typeof(SmsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<SmsResponse>> ScheduleSms([FromBody] SendSmsRequest request, CancellationToken cancellationToken = default)
    {
        if (!request.ScheduledAt.HasValue)
        {
            return BadRequest(new { error = "ScheduledAt is required for scheduled messages" });
        }

        if (request.ScheduledAt.Value <= DateTime.UtcNow)
        {
            return BadRequest(new { error = "ScheduledAt must be in the future" });
        }

        try
        {
            var response = await _smsService.ScheduleSmsAsync(request, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to schedule SMS");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Cancel a scheduled SMS message
    /// </summary>
    /// <param name="messageId">Message ID to cancel</param>
    /// <param name="provider">Provider that scheduled the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success status</returns>
    [HttpDelete("schedule/{messageId}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> CancelScheduledSms(
        [FromRoute] string messageId, 
        [FromQuery] string provider,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _smsService.CancelScheduledSmsAsync(messageId, provider, cancellationToken);
            
            if (success)
            {
                return Ok(new { success = true, message = "Scheduled SMS cancelled successfully" });
            }
            
            return NotFound(new { error = "Scheduled message not found or cannot be cancelled" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel scheduled SMS {MessageId}", messageId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get the status of a previously sent message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="provider">Provider that sent the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message status</returns>
    [HttpGet("status/{messageId}")]
    [ProducesResponseType(typeof(MessageStatusResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<MessageStatusResponse>> GetMessageStatus(
        [FromRoute] string messageId,
        [FromQuery] string provider,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.GetMessageStatusAsync(messageId, provider, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get message status for {MessageId}", messageId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get detailed message information
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="provider">Provider that sent the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detailed message information</returns>
    [HttpGet("details/{messageId}")]
    [ProducesResponseType(typeof(MessageDetailsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<MessageDetailsResponse>> GetMessageDetails(
        [FromRoute] string messageId,
        [FromQuery] string provider,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.GetMessageDetailsAsync(messageId, provider, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get message details for {MessageId}", messageId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get delivery report for a message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="provider">Provider that sent the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery report</returns>
    [HttpGet("delivery-report/{messageId}")]
    [ProducesResponseType(typeof(DeliveryReportResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<DeliveryReportResponse>> GetDeliveryReport(
        [FromRoute] string messageId,
        [FromQuery] string provider,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.GetDeliveryReportAsync(messageId, provider, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get delivery report for {MessageId}", messageId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get status of multiple messages
    /// </summary>
    /// <param name="request">Message IDs and provider</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Status responses for each message</returns>
    [HttpPost("status/batch")]
    [ProducesResponseType(typeof(List<MessageStatusResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<MessageStatusResponse>>> GetBulkMessageStatus(
        [FromBody] GetBulkMessageStatusRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var responses = await _smsService.GetBulkMessageStatusAsync(request.MessageIds, request.Provider, cancellationToken);
            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get bulk message status");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get estimated cost for sending an SMS
    /// </summary>
    /// <param name="request">Cost estimation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Estimated cost and currency</returns>
    [HttpPost("cost-estimate")]
    [ProducesResponseType(typeof(CostEstimateResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<CostEstimateResponse>> GetEstimatedCost(
        [FromBody] GetCostEstimateRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.GetEstimatedCostAsync(request, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get cost estimate");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get SMS message history
    /// </summary>
    /// <param name="request">History query parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS message history</returns>
    [HttpGet("history")]
    [ProducesResponseType(typeof(SmsHistoryResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<SmsHistoryResponse>> GetSmsHistory(
        [FromQuery] GetSmsHistoryRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.GetSmsHistoryAsync(request, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get SMS history");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Send SMS with template
    /// </summary>
    /// <param name="request">Template SMS request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response</returns>
    [HttpPost("send/template")]
    [ProducesResponseType(typeof(SmsResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<SmsResponse>> SendTemplateSms(
        [FromBody] SendTemplateSmsRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.SendTemplateSmsAsync(request, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send template SMS");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Validate phone number
    /// </summary>
    /// <param name="phoneNumber">Phone number to validate</param>
    /// <param name="countryCode">Optional country code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Phone validation result</returns>
    [HttpGet("validate/phone")]
    [ProducesResponseType(typeof(PhoneValidationResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<PhoneValidationResponse>> ValidatePhoneNumber(
        [FromQuery, Required] string phoneNumber,
        [FromQuery] string? countryCode = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.ValidatePhoneNumberAsync(phoneNumber, countryCode, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate phone number");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Validate message content
    /// </summary>
    /// <param name="request">Message validation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message validation result</returns>
    [HttpPost("validate/message")]
    [ProducesResponseType(typeof(MessageValidationResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<MessageValidationResponse>> ValidateMessage(
        [FromBody] ValidateMessageRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _smsService.ValidateMessageAsync(request.Message, request.MessageType, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate message");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
}

/// <summary>
/// Request model for bulk message status
/// </summary>
public class GetBulkMessageStatusRequest
{
    /// <summary>
    /// List of message IDs
    /// </summary>
    [Required]
    public List<string> MessageIds { get; set; } = new();

    /// <summary>
    /// Provider name
    /// </summary>
    [Required]
    public string Provider { get; set; } = string.Empty;
}

/// <summary>
/// Request model for message validation
/// </summary>
public class ValidateMessageRequest
{
    /// <summary>
    /// Message content to validate
    /// </summary>
    [Required]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Message type
    /// </summary>
    public MessageType MessageType { get; set; } = MessageType.Text;
}
