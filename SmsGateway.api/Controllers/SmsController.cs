using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using SmsGateway.api.Interfaces;
using SmsGateway.api.Models;

namespace SmsGateway.api.Controllers;

/// <summary>
/// SMS operations controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class SmsController : ControllerBase
{
    private readonly ISmsService _smsService;
    private readonly ILogger<SmsController> _logger;

    public SmsController(ISmsService smsService, ILogger<SmsController> logger)
    {
        _smsService = smsService;
        _logger = logger;
    }

    /// <summary>
    /// Send a single SMS message
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response with delivery details</returns>
    [HttpPost("send")]
    [ProducesResponseType(typeof(SmsResponse), 200)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<SmsResponse>> SendSms([FromBody] SmsRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Received SMS request to {To} via provider {Provider}", request.To, request.Provider ?? "auto");
            
            var response = await _smsService.SendSmsAsync(request, cancellationToken);
            
            if (response.Success)
            {
                return Ok(response);
            }
            else
            {
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS to {To}", request.To);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Send a single SMS message using a specific provider
    /// </summary>
    /// <param name="providerName">Name of the provider to use</param>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response with delivery details</returns>
    [HttpPost("send/{providerName}")]
    [ProducesResponseType(typeof(SmsResponse), 200)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<SmsResponse>> SendSmsWithProvider(
        [FromRoute] string providerName, 
        [FromBody] SmsRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Received SMS request to {To} via specific provider {Provider}", request.To, providerName);
            
            var response = await _smsService.SendSmsAsync(request, providerName, cancellationToken);
            
            if (response.Success)
            {
                return Ok(response);
            }
            else if (response.ErrorCode == "PROVIDER_NOT_FOUND")
            {
                return NotFound(response);
            }
            else
            {
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS to {To} via provider {Provider}", request.To, providerName);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Send multiple SMS messages in batch
    /// </summary>
    /// <param name="requests">Collection of SMS requests</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of SMS responses</returns>
    [HttpPost("send/batch")]
    [ProducesResponseType(typeof(IEnumerable<SmsResponse>), 200)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IEnumerable<SmsResponse>>> SendBatchSms(
        [FromBody] IEnumerable<SmsRequest> requests, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var requestList = requests.ToList();
            _logger.LogInformation("Received batch SMS request for {Count} messages", requestList.Count);
            
            if (requestList.Count == 0)
            {
                return BadRequest(new { error = "No SMS requests provided" });
            }

            if (requestList.Count > 100) // Limit batch size
            {
                return BadRequest(new { error = "Batch size cannot exceed 100 messages" });
            }
            
            var responses = await _smsService.SendBatchSmsAsync(requestList, cancellationToken);
            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending batch SMS");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get the status of a previously sent message
    /// </summary>
    /// <param name="messageId">Message ID returned from send operation</param>
    /// <param name="provider">Provider that sent the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current message status</returns>
    [HttpGet("status/{messageId}")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetMessageStatus(
        [FromRoute] string messageId,
        [FromQuery, Required] string provider,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting status for message {MessageId} from provider {Provider}", messageId, provider);
            
            var status = await _smsService.GetMessageStatusAsync(messageId, provider, cancellationToken);
            
            return Ok(new
            {
                messageId,
                provider,
                status = status.ToString(),
                checkedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message status for {MessageId}", messageId);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get estimated cost for sending an SMS
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="provider">Optional specific provider name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Estimated cost and currency</returns>
    [HttpPost("cost/estimate")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetEstimatedCost(
        [FromBody] SmsRequest request,
        [FromQuery] string? provider = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting cost estimate for SMS to {To} via provider {Provider}", request.To, provider ?? "auto");
            
            var costEstimate = await _smsService.GetEstimatedCostAsync(request, provider, cancellationToken);
            
            if (costEstimate.HasValue)
            {
                return Ok(new
                {
                    cost = costEstimate.Value.cost,
                    currency = costEstimate.Value.currency,
                    provider = provider ?? "auto",
                    estimatedAt = DateTime.UtcNow
                });
            }
            else
            {
                return BadRequest(new { error = "Unable to estimate cost", message = "No suitable provider found or cost estimation not available" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating SMS cost");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }
}
