using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Core.Models.Common;
using System.ComponentModel.DataAnnotations;

namespace SmsGateway.Api.Controllers;

/// <summary>
/// Enhanced provider management API controller
/// </summary>
[ApiController]
[Route("api/v2/[controller]")]
[Authorize(Roles = "Administrator")]
[Produces("application/json")]
public class ProvidersController : ControllerBase
{
    private readonly IProviderService _providerService;
    private readonly ILogger<ProvidersController> _logger;

    public ProvidersController(IProviderService providerService, ILogger<ProvidersController> logger)
    {
        _providerService = providerService;
        _logger = logger;
    }

    /// <summary>
    /// Get all available SMS providers
    /// </summary>
    /// <param name="includeDisabled">Include disabled providers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of provider information</returns>
    [HttpGet]
    [ProducesResponseType(typeof(List<ProviderInfoResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<ProviderInfoResponse>>> GetProviders(
        [FromQuery] bool includeDisabled = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var providers = await _providerService.GetProvidersAsync(includeDisabled, cancellationToken);
            return Ok(providers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get providers");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get specific provider information
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Provider information</returns>
    [HttpGet("{providerName}")]
    [ProducesResponseType(typeof(ProviderInfoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ProviderInfoResponse>> GetProvider(
        [FromRoute] string providerName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = await _providerService.GetProviderAsync(providerName, cancellationToken);
            
            if (provider == null)
            {
                return NotFound(new { error = $"Provider '{providerName}' not found" });
            }
            
            return Ok(provider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get provider {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get provider health status
    /// </summary>
    /// <param name="providerName">Optional specific provider name</param>
    /// <param name="forceRefresh">Force fresh health check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Provider health status</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(List<ProviderHealthResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<ProviderHealthResponse>>> GetProviderHealth(
        [FromQuery] string? providerName = null,
        [FromQuery] bool forceRefresh = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new GetProviderHealthRequest
            {
                ProviderName = providerName,
                ForceRefresh = forceRefresh
            };
            
            var healthStatus = await _providerService.GetProviderHealthAsync(request, cancellationToken);
            return Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get provider health");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get provider statistics
    /// </summary>
    /// <param name="providerName">Optional specific provider name</param>
    /// <param name="fromDate">Start date for statistics</param>
    /// <param name="toDate">End date for statistics</param>
    /// <param name="includeDetails">Include detailed breakdown</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Provider statistics</returns>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(List<ProviderStatisticsResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<ProviderStatisticsResponse>>> GetProviderStatistics(
        [FromQuery] string? providerName = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] bool includeDetails = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new GetProviderStatisticsRequest
            {
                ProviderName = providerName,
                FromDate = fromDate,
                ToDate = toDate,
                IncludeDetails = includeDetails
            };
            
            var statistics = await _providerService.GetProviderStatisticsAsync(request, cancellationToken);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get provider statistics");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Test provider connectivity and configuration
    /// </summary>
    /// <param name="request">Provider test request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Test result</returns>
    [HttpPost("test")]
    [ProducesResponseType(typeof(ProviderTestResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<ProviderTestResponse>> TestProvider(
        [FromBody] TestProviderRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _providerService.TestProviderAsync(request, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test provider {ProviderName}", request.ProviderName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Update provider configuration
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="request">Configuration update request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated provider information</returns>
    [HttpPut("{providerName}/configuration")]
    [ProducesResponseType(typeof(ProviderInfoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ProviderInfoResponse>> UpdateProviderConfiguration(
        [FromRoute] string providerName,
        [FromBody] UpdateProviderConfigurationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            request.ProviderName = providerName;
            var provider = await _providerService.UpdateProviderConfigurationAsync(request, cancellationToken);
            
            if (provider == null)
            {
                return NotFound(new { error = $"Provider '{providerName}' not found" });
            }
            
            return Ok(provider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update provider configuration for {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get pricing information for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="countryCode">Optional country code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Pricing information</returns>
    [HttpGet("{providerName}/pricing")]
    [ProducesResponseType(typeof(PricingInfoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<PricingInfoResponse>> GetProviderPricing(
        [FromRoute] string providerName,
        [FromQuery] string? countryCode = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var pricing = await _providerService.GetProviderPricingAsync(providerName, countryCode, cancellationToken);
            
            if (pricing == null)
            {
                return NotFound(new { error = $"Pricing information not available for provider '{providerName}'" });
            }
            
            return Ok(pricing);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get pricing for provider {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get account balance for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Account balance information</returns>
    [HttpGet("{providerName}/balance")]
    [ProducesResponseType(typeof(AccountBalanceResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<AccountBalanceResponse>> GetProviderBalance(
        [FromRoute] string providerName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var balance = await _providerService.GetProviderBalanceAsync(providerName, cancellationToken);
            
            if (balance == null)
            {
                return NotFound(new { error = $"Balance information not available for provider '{providerName}'" });
            }
            
            return Ok(balance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get balance for provider {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get rate limit status for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Rate limit information</returns>
    [HttpGet("{providerName}/rate-limit")]
    [ProducesResponseType(typeof(RateLimitResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<RateLimitResponse>> GetProviderRateLimit(
        [FromRoute] string providerName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var rateLimit = await _providerService.GetProviderRateLimitAsync(providerName, cancellationToken);
            
            if (rateLimit == null)
            {
                return NotFound(new { error = $"Rate limit information not available for provider '{providerName}'" });
            }
            
            return Ok(rateLimit);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get rate limit for provider {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get loaded plugins
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of loaded plugins</returns>
    [HttpGet("plugins")]
    [ProducesResponseType(typeof(List<PluginInfoResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<PluginInfoResponse>>> GetLoadedPlugins(CancellationToken cancellationToken = default)
    {
        try
        {
            var plugins = await _providerService.GetLoadedPluginsAsync(cancellationToken);
            return Ok(plugins);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get loaded plugins");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Load a plugin
    /// </summary>
    /// <param name="request">Plugin load request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Plugin load result</returns>
    [HttpPost("plugins/load")]
    [ProducesResponseType(typeof(PluginLoadResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<PluginLoadResponse>> LoadPlugin(
        [FromBody] LoadPluginRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _providerService.LoadPluginAsync(request, cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load plugin from {PluginPath}", request.PluginPath);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Unload a plugin
    /// </summary>
    /// <param name="pluginName">Plugin name to unload</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Unload result</returns>
    [HttpDelete("plugins/{pluginName}")]
    [ProducesResponseType(typeof(PluginLoadResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<PluginLoadResponse>> UnloadPlugin(
        [FromRoute] string pluginName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new UnloadPluginRequest { PluginName = pluginName };
            var response = await _providerService.UnloadPluginAsync(request, cancellationToken);
            
            if (!response.Success)
            {
                return NotFound(new { error = $"Plugin '{pluginName}' not found or cannot be unloaded" });
            }
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unload plugin {PluginName}", pluginName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }

    /// <summary>
    /// Configure webhook for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="request">Webhook configuration request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Webhook configuration result</returns>
    [HttpPost("{providerName}/webhook")]
    [ProducesResponseType(typeof(WebhookConfigResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<WebhookConfigResponse>> ConfigureWebhook(
        [FromRoute] string providerName,
        [FromBody] ConfigureWebhookRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _providerService.ConfigureWebhookAsync(providerName, request, cancellationToken);
            
            if (!response.Success)
            {
                return BadRequest(new { error = response.ErrorMessage });
            }
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to configure webhook for provider {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
}

/// <summary>
/// Request model for webhook configuration
/// </summary>
public class ConfigureWebhookRequest
{
    /// <summary>
    /// Webhook URL
    /// </summary>
    [Required]
    [Url]
    public string WebhookUrl { get; set; } = string.Empty;

    /// <summary>
    /// Events to subscribe to
    /// </summary>
    [Required]
    public List<WebhookEvent> Events { get; set; } = new();

    /// <summary>
    /// Optional webhook secret for verification
    /// </summary>
    public string? Secret { get; set; }
}
