using Microsoft.AspNetCore.Mvc;
using SmsGateway.api.Interfaces;

namespace SmsGateway.api.Controllers;

/// <summary>
/// SMS providers management controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ProvidersController : ControllerBase
{
    private readonly ISmsService _smsService;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<ProvidersController> _logger;

    public ProvidersController(ISmsService smsService, IPluginManager pluginManager, ILogger<ProvidersController> logger)
    {
        _smsService = smsService;
        _pluginManager = pluginManager;
        _logger = logger;
    }

    /// <summary>
    /// Get all available SMS providers
    /// </summary>
    /// <returns>List of available providers with their information</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<ProviderInfo>), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IEnumerable<ProviderInfo>>> GetProviders()
    {
        try
        {
            _logger.LogInformation("Getting all available SMS providers");
            
            var providers = await _smsService.GetAvailableProvidersAsync();
            return Ok(providers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS providers");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get information about a specific provider
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Provider information</returns>
    [HttpGet("{providerName}")]
    [ProducesResponseType(typeof(ProviderInfo), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<ProviderInfo>> GetProvider(string providerName)
    {
        try
        {
            _logger.LogInformation("Getting information for provider {ProviderName}", providerName);
            
            var providers = await _smsService.GetAvailableProvidersAsync();
            var provider = providers.FirstOrDefault(p => p.Name.Equals(providerName, StringComparison.OrdinalIgnoreCase));
            
            if (provider == null)
            {
                return NotFound(new { error = "Provider not found", providerName });
            }
            
            return Ok(provider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting provider {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get statistics for all providers or a specific provider
    /// </summary>
    /// <param name="providerName">Optional provider name (if not specified, returns all)</param>
    /// <returns>Provider statistics</returns>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(IEnumerable<ProviderStatistics>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IEnumerable<ProviderStatistics>>> GetProviderStatistics([FromQuery] string? providerName = null)
    {
        try
        {
            _logger.LogInformation("Getting statistics for provider {ProviderName}", providerName ?? "all");
            
            var statistics = await _smsService.GetProviderStatisticsAsync(providerName);
            
            if (!string.IsNullOrEmpty(providerName) && !statistics.Any())
            {
                return NotFound(new { error = "Provider not found or no statistics available", providerName });
            }
            
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting provider statistics");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Perform health check on all providers
    /// </summary>
    /// <returns>Health status of all providers</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(IEnumerable<ProviderHealthStatus>), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IEnumerable<ProviderHealthStatus>>> HealthCheck()
    {
        try
        {
            _logger.LogInformation("Performing health check on all SMS providers");
            
            var healthStatuses = await _smsService.HealthCheckAsync();
            return Ok(healthStatuses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Perform health check on a specific provider
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Health status of the provider</returns>
    [HttpGet("{providerName}/health")]
    [ProducesResponseType(typeof(ProviderHealthStatus), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<ProviderHealthStatus>> HealthCheckProvider(string providerName)
    {
        try
        {
            _logger.LogInformation("Performing health check on provider {ProviderName}", providerName);
            
            var provider = _pluginManager.GetProvider(providerName);
            if (provider == null)
            {
                return NotFound(new { error = "Provider not found", providerName });
            }
            
            var healthResult = await provider.HealthCheckAsync();
            var healthStatus = new ProviderHealthStatus
            {
                ProviderName = provider.Name,
                IsHealthy = healthResult.IsHealthy,
                Message = healthResult.Message,
                ResponseTime = healthResult.ResponseTime,
                Data = healthResult.Data
            };
            
            return Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check on provider {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get enabled providers ordered by priority
    /// </summary>
    /// <returns>List of enabled providers in priority order</returns>
    [HttpGet("enabled")]
    [ProducesResponseType(typeof(IEnumerable<ProviderInfo>), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IEnumerable<ProviderInfo>>> GetEnabledProviders()
    {
        try
        {
            _logger.LogInformation("Getting enabled SMS providers");
            
            var allProviders = await _smsService.GetAvailableProvidersAsync();
            var enabledProviders = allProviders
                .Where(p => p.IsEnabled && p.IsAvailable)
                .OrderBy(p => p.Priority);
            
            return Ok(enabledProviders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting enabled SMS providers");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Get provider configuration (sensitive data excluded)
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Provider configuration (without sensitive data)</returns>
    [HttpGet("{providerName}/config")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public ActionResult GetProviderConfiguration(string providerName)
    {
        try
        {
            _logger.LogInformation("Getting configuration for provider {ProviderName}", providerName);
            
            var configuration = _pluginManager.GetProviderConfiguration(providerName);
            if (configuration == null)
            {
                return NotFound(new { error = "Provider not found", providerName });
            }
            
            // Return configuration without sensitive data
            var safeConfig = new
            {
                configuration.Name,
                configuration.DisplayName,
                configuration.Enabled,
                configuration.Priority,
                configuration.MaxRetries,
                configuration.TimeoutSeconds,
                configuration.RateLimitPerMinute,
                configuration.DefaultSenderId,
                configuration.SupportedCountries,
                // Exclude sensitive settings like API keys
                SettingsCount = configuration.Settings.Count
            };
            
            return Ok(safeConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting configuration for provider {ProviderName}", providerName);
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }
}
