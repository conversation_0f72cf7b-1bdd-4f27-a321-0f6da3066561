using SmsGateway.api.Interfaces;
using SmsGateway.api.Providers;
using SmsGateway.api.Services;

namespace SmsGateway.api.Extensions;

/// <summary>
/// Extension methods for IServiceCollection to register SMS gateway services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Add SMS Gateway services to the dependency injection container
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Application configuration</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddSmsGateway(this IServiceCollection services, IConfiguration configuration)
    {
        // Register core services
        services.AddSingleton<IPluginManager, PluginManager>();
        services.AddScoped<ISmsService, SmsService>();
        
        // Register HTTP client for providers
        services.AddHttpClient<TwilioSmsProvider>(client =>
        {
            client.DefaultRequestHeaders.Add("User-Agent", "SmsGateway/1.0");
        });
        
        // Register built-in providers
        services.AddTransient<TwilioSmsProvider>();
        services.AddTransient<MockSmsProvider>();
        
        // Add logging
        services.AddLogging();
        
        return services;
    }

    /// <summary>
    /// Initialize SMS Gateway plugins and providers
    /// </summary>
    /// <param name="serviceProvider">Service provider</param>
    /// <returns>Task representing the async operation</returns>
    public static async Task InitializeSmsGatewayAsync(this IServiceProvider serviceProvider)
    {
        var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
        var logger = serviceProvider.GetRequiredService<ILogger<IPluginManager>>();
        
        try
        {
            logger.LogInformation("Initializing SMS Gateway plugins...");
            await pluginManager.LoadPluginsAsync();
            logger.LogInformation("SMS Gateway plugins initialized successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to initialize SMS Gateway plugins");
            throw;
        }
    }
}
