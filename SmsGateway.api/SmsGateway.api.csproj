<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.2.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0"/>
        <PackageReference Include="Hangfire.Core" Version="1.8.14"/>
        <PackageReference Include="Hangfire.SqlServer" Version="1.8.14"/>
        <PackageReference Include="Hangfire.AspNetCore" Version="1.8.14"/>
        <PackageReference Include="StackExchange.Redis" Version="2.8.16"/>
        <PackageReference Include="DotLiquid" Version="2.2.692"/>
        <PackageReference Include="Finbuckle.MultiTenant" Version="7.0.2"/>
        <PackageReference Include="Finbuckle.MultiTenant.AspNetCore" Version="7.0.2"/>
        <PackageReference Include="Finbuckle.MultiTenant.EntityFrameworkCore" Version="7.0.2"/>
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.2"/>
        <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.6.1"/>
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\SmsGateway.Core\SmsGateway.Core.csproj" />
        <ProjectReference Include="..\SmsGateway.Database\SmsGateway.Database.csproj" />
        <ProjectReference Include="..\SmsGateway.Services\SmsGateway.Services.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Plugins\" />
    </ItemGroup>

</Project>
