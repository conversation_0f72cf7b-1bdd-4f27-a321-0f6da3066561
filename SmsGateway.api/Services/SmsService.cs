using System.Diagnostics;
using SmsGateway.api.Interfaces;
using SmsGateway.api.Models;

namespace SmsGateway.api.Services;

/// <summary>
/// Main SMS service implementation
/// </summary>
public class SmsService : ISmsService
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<SmsService> _logger;

    public SmsService(IPluginManager pluginManager, ILogger<SmsService> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public async Task<SmsResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        var provider = _pluginManager.GetBestProvider(request);
        if (provider == null)
        {
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = "No suitable SMS provider available",
                ErrorCode = "NO_PROVIDER"
            };
        }

        return await SendSmsWithProviderAsync(request, provider, cancellationToken);
    }

    public async Task<SmsResponse> SendSmsAsync(SmsRequest request, string providerName, CancellationToken cancellationToken = default)
    {
        var provider = _pluginManager.GetProvider(providerName);
        if (provider == null)
        {
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = $"SMS provider '{providerName}' not found",
                ErrorCode = "PROVIDER_NOT_FOUND"
            };
        }

        if (!_pluginManager.IsProviderEnabled(providerName))
        {
            return new SmsResponse
            {
                Success = false,
                Status = SmsStatus.Failed,
                ErrorMessage = $"SMS provider '{providerName}' is disabled",
                ErrorCode = "PROVIDER_DISABLED"
            };
        }

        return await SendSmsWithProviderAsync(request, provider, cancellationToken);
    }

    public async Task<IEnumerable<SmsResponse>> SendBatchSmsAsync(IEnumerable<SmsRequest> requests, CancellationToken cancellationToken = default)
    {
        var tasks = requests.Select(request => SendSmsAsync(request, cancellationToken));
        return await Task.WhenAll(tasks);
    }

    public async Task<SmsStatus> GetMessageStatusAsync(string messageId, string providerName, CancellationToken cancellationToken = default)
    {
        var provider = _pluginManager.GetProvider(providerName);
        if (provider == null)
        {
            _logger.LogWarning("Provider {ProviderName} not found for status check of message {MessageId}", providerName, messageId);
            return SmsStatus.Unknown;
        }

        try
        {
            return await provider.GetMessageStatusAsync(messageId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get message status for {MessageId} from provider {ProviderName}", messageId, providerName);
            return SmsStatus.Unknown;
        }
    }

    public async Task<(decimal cost, string currency)?> GetEstimatedCostAsync(SmsRequest request, string? providerName = null, CancellationToken cancellationToken = default)
    {
        ISmsProvider? provider;
        
        if (!string.IsNullOrEmpty(providerName))
        {
            provider = _pluginManager.GetProvider(providerName);
        }
        else
        {
            provider = _pluginManager.GetBestProvider(request);
        }

        if (provider == null)
        {
            return null;
        }

        try
        {
            return await provider.GetEstimatedCostAsync(request, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get estimated cost from provider {ProviderName}", provider.Name);
            return null;
        }
    }

    public async Task<IEnumerable<ProviderInfo>> GetAvailableProvidersAsync()
    {
        var providers = _pluginManager.GetAllProviders();
        var providerInfos = new List<ProviderInfo>();

        foreach (var provider in providers)
        {
            var configuration = _pluginManager.GetProviderConfiguration(provider.Name);
            providerInfos.Add(new ProviderInfo
            {
                Name = provider.Name,
                DisplayName = provider.DisplayName,
                Version = provider.Version,
                Description = provider.Description,
                IsEnabled = _pluginManager.IsProviderEnabled(provider.Name),
                IsAvailable = provider.IsAvailable,
                Priority = configuration?.Priority ?? int.MaxValue,
                SupportedCountries = configuration?.SupportedCountries ?? new List<string>()
            });
        }

        return providerInfos.OrderBy(p => p.Priority);
    }

    public async Task<IEnumerable<ProviderStatistics>> GetProviderStatisticsAsync(string? providerName = null)
    {
        if (!string.IsNullOrEmpty(providerName))
        {
            var stats = _pluginManager.GetProviderStatistics(providerName);
            return stats != null ? new[] { stats } : Enumerable.Empty<ProviderStatistics>();
        }

        var allProviders = _pluginManager.GetAllProviders();
        var statisticsList = new List<ProviderStatistics>();

        foreach (var provider in allProviders)
        {
            var stats = _pluginManager.GetProviderStatistics(provider.Name);
            if (stats != null)
            {
                statisticsList.Add(stats);
            }
        }

        return statisticsList;
    }

    public async Task<IEnumerable<ProviderHealthStatus>> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        var providers = _pluginManager.GetAllProviders();
        var healthChecks = new List<Task<ProviderHealthStatus>>();

        foreach (var provider in providers)
        {
            healthChecks.Add(CheckProviderHealthAsync(provider, cancellationToken));
        }

        return await Task.WhenAll(healthChecks);
    }

    private async Task<SmsResponse> SendSmsWithProviderAsync(SmsRequest request, ISmsProvider provider, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Sending SMS via provider {ProviderName} to {To}", provider.Name, request.To);
            
            var response = await provider.SendSmsAsync(request, cancellationToken);
            stopwatch.Stop();
            
            response.Provider = provider.Name;
            
            // Update statistics
            if (_pluginManager is PluginManager pluginManager)
            {
                pluginManager.UpdateStatistics(provider.Name, response.Success, stopwatch.Elapsed, response.ErrorMessage);
            }
            
            if (response.Success)
            {
                _logger.LogInformation("SMS sent successfully via {ProviderName}. MessageId: {MessageId}", provider.Name, response.MessageId);
            }
            else
            {
                _logger.LogWarning("SMS failed via {ProviderName}. Error: {ErrorMessage}", provider.Name, response.ErrorMessage);
            }
            
            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            _logger.LogError(ex, "Exception occurred while sending SMS via provider {ProviderName}", provider.Name);
            
            // Update statistics
            if (_pluginManager is PluginManager pluginManager)
            {
                pluginManager.UpdateStatistics(provider.Name, false, stopwatch.Elapsed, ex.Message);
            }
            
            return new SmsResponse
            {
                Success = false,
                Provider = provider.Name,
                Status = SmsStatus.Failed,
                ErrorMessage = ex.Message,
                ErrorCode = "PROVIDER_EXCEPTION"
            };
        }
    }

    private async Task<ProviderHealthStatus> CheckProviderHealthAsync(ISmsProvider provider, CancellationToken cancellationToken)
    {
        try
        {
            var healthResult = await provider.HealthCheckAsync(cancellationToken);
            return new ProviderHealthStatus
            {
                ProviderName = provider.Name,
                IsHealthy = healthResult.IsHealthy,
                Message = healthResult.Message,
                ResponseTime = healthResult.ResponseTime,
                Data = healthResult.Data
            };
        }
        catch (Exception ex)
        {
            return new ProviderHealthStatus
            {
                ProviderName = provider.Name,
                IsHealthy = false,
                Message = ex.Message,
                ResponseTime = TimeSpan.Zero
            };
        }
    }
}
