using System.Collections.Concurrent;
using System.Reflection;
using SmsGateway.api.Interfaces;
using SmsGateway.api.Models;

namespace SmsGateway.api.Services;

/// <summary>
/// Manages SMS provider plugins
/// </summary>
public class PluginManager : IPluginManager
{
    private readonly ConcurrentDictionary<string, ISmsProvider> _providers = new();
    private readonly ConcurrentDictionary<string, ProviderConfiguration> _configurations = new();
    private readonly ConcurrentDictionary<string, ProviderStatistics> _statistics = new();
    private readonly ILogger<PluginManager> _logger;
    private readonly IConfiguration _configuration;

    public PluginManager(ILogger<PluginManager> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task LoadPluginsAsync()
    {
        _logger.LogInformation("Loading SMS provider plugins...");

        // Load built-in providers first
        await LoadBuiltInProvidersAsync();

        // Load external plugins from plugins directory
        await LoadExternalPluginsAsync();

        _logger.LogInformation("Loaded {Count} SMS providers", _providers.Count);
    }

    public async Task RegisterProviderAsync(ISmsProvider provider, ProviderConfiguration configuration)
    {
        try
        {
            // Validate configuration
            var validationResult = await provider.ValidateConfigurationAsync(configuration);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"Invalid configuration for provider {provider.Name}: {string.Join(", ", validationResult.Errors)}");
            }

            // Initialize provider
            await provider.InitializeAsync(configuration);

            // Register provider
            _providers.AddOrUpdate(provider.Name, provider, (key, oldValue) => provider);
            _configurations.AddOrUpdate(provider.Name, configuration, (key, oldValue) => configuration);
            
            // Initialize statistics
            _statistics.AddOrUpdate(provider.Name, new ProviderStatistics
            {
                ProviderName = provider.Name,
                IsHealthy = provider.IsAvailable
            }, (key, oldValue) => oldValue);

            _logger.LogInformation("Registered SMS provider: {ProviderName} ({DisplayName})", provider.Name, provider.DisplayName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register provider {ProviderName}", provider.Name);
            throw;
        }
    }

    public async Task UnregisterProviderAsync(string providerName)
    {
        if (_providers.TryRemove(providerName, out var provider))
        {
            _configurations.TryRemove(providerName, out _);
            _statistics.TryRemove(providerName, out _);
            
            _logger.LogInformation("Unregistered SMS provider: {ProviderName}", providerName);
        }
        
        await Task.CompletedTask;
    }

    public ISmsProvider? GetProvider(string providerName)
    {
        _providers.TryGetValue(providerName, out var provider);
        return provider;
    }

    public IEnumerable<ISmsProvider> GetAllProviders()
    {
        return _providers.Values;
    }

    public IEnumerable<ISmsProvider> GetEnabledProviders()
    {
        return _providers.Values
            .Where(p => IsProviderEnabled(p.Name) && p.IsAvailable)
            .OrderBy(p => _configurations.TryGetValue(p.Name, out var config) ? config.Priority : int.MaxValue);
    }

    public ISmsProvider? GetBestProvider(SmsRequest request)
    {
        var enabledProviders = GetEnabledProviders();
        
        // If specific provider requested, try to use it
        if (!string.IsNullOrEmpty(request.Provider))
        {
            var requestedProvider = enabledProviders.FirstOrDefault(p => p.Name.Equals(request.Provider, StringComparison.OrdinalIgnoreCase));
            if (requestedProvider != null)
            {
                return requestedProvider;
            }
        }

        // Extract country code from phone number (basic implementation)
        var countryCode = ExtractCountryCode(request.To);
        
        // Find providers that support the country
        var suitableProviders = enabledProviders
            .Where(p => string.IsNullOrEmpty(countryCode) || p.SupportsCountry(countryCode))
            .ToList();

        // Return the highest priority (lowest priority number) provider
        return suitableProviders.FirstOrDefault();
    }

    public ProviderConfiguration? GetProviderConfiguration(string providerName)
    {
        _configurations.TryGetValue(providerName, out var configuration);
        return configuration;
    }

    public async Task UpdateProviderConfigurationAsync(string providerName, ProviderConfiguration configuration)
    {
        if (_providers.TryGetValue(providerName, out var provider))
        {
            var validationResult = await provider.ValidateConfigurationAsync(configuration);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"Invalid configuration: {string.Join(", ", validationResult.Errors)}");
            }

            await provider.InitializeAsync(configuration);
            _configurations.AddOrUpdate(providerName, configuration, (key, oldValue) => configuration);
            
            _logger.LogInformation("Updated configuration for provider: {ProviderName}", providerName);
        }
        else
        {
            throw new ArgumentException($"Provider {providerName} not found");
        }
    }

    public bool IsProviderEnabled(string providerName)
    {
        if (_configurations.TryGetValue(providerName, out var config))
        {
            return config.Enabled;
        }
        return false;
    }

    public ProviderStatistics? GetProviderStatistics(string providerName)
    {
        _statistics.TryGetValue(providerName, out var statistics);
        return statistics;
    }

    private async Task LoadBuiltInProvidersAsync()
    {
        // This method will load built-in providers from the current assembly
        var assembly = Assembly.GetExecutingAssembly();
        var providerTypes = assembly.GetTypes()
            .Where(t => typeof(ISmsProvider).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract);

        foreach (var providerType in providerTypes)
        {
            try
            {
                if (Activator.CreateInstance(providerType) is ISmsProvider provider)
                {
                    // Load configuration from appsettings
                    var configSection = _configuration.GetSection($"SmsProviders:{provider.Name}");
                    if (configSection.Exists())
                    {
                        var config = CreateConfigurationFromSection(configSection);
                        if (config != null)
                        {
                            await RegisterProviderAsync(provider, config);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load built-in provider {ProviderType}", providerType.Name);
            }
        }
    }

    private async Task LoadExternalPluginsAsync()
    {
        var pluginsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Plugins");
        if (!Directory.Exists(pluginsPath))
        {
            _logger.LogInformation("Plugins directory not found: {PluginsPath}", pluginsPath);
            return;
        }

        var dllFiles = Directory.GetFiles(pluginsPath, "*.dll", SearchOption.AllDirectories);
        
        foreach (var dllFile in dllFiles)
        {
            try
            {
                var assembly = Assembly.LoadFrom(dllFile);
                var providerTypes = assembly.GetTypes()
                    .Where(t => typeof(ISmsProvider).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract);

                foreach (var providerType in providerTypes)
                {
                    if (Activator.CreateInstance(providerType) is ISmsProvider provider)
                    {
                        var configSection = _configuration.GetSection($"SmsProviders:{provider.Name}");
                        if (configSection.Exists())
                        {
                            var config = CreateConfigurationFromSection(configSection);
                            if (config != null)
                            {
                                await RegisterProviderAsync(provider, config);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load plugin from {DllFile}", dllFile);
            }
        }
    }

    private ProviderConfiguration? CreateConfigurationFromSection(IConfigurationSection section)
    {
        var type = section["Type"];
        return type?.ToLowerInvariant() switch
        {
            "apikey" => section.Get<ApiKeyProviderConfiguration>(),
            "usernamepassword" => section.Get<UsernamePasswordProviderConfiguration>(),
            _ => section.Get<ProviderConfiguration>()
        };
    }

    private string? ExtractCountryCode(string phoneNumber)
    {
        // Basic country code extraction - in production, use a proper phone number library
        if (phoneNumber.StartsWith("+"))
        {
            if (phoneNumber.StartsWith("+1")) return "US";
            if (phoneNumber.StartsWith("+44")) return "GB";
            if (phoneNumber.StartsWith("+49")) return "DE";
            // Add more country codes as needed
        }
        return null;
    }

    public void UpdateStatistics(string providerName, bool success, TimeSpan responseTime, string? error = null)
    {
        _statistics.AddOrUpdate(providerName, 
            new ProviderStatistics
            {
                ProviderName = providerName,
                TotalMessagesSent = 1,
                SuccessfulMessages = success ? 1 : 0,
                FailedMessages = success ? 0 : 1,
                AverageResponseTime = responseTime,
                LastUsed = DateTime.UtcNow,
                IsHealthy = success,
                LastError = error
            },
            (key, existing) =>
            {
                existing.TotalMessagesSent++;
                if (success)
                    existing.SuccessfulMessages++;
                else
                    existing.FailedMessages++;
                
                // Update average response time
                existing.AverageResponseTime = TimeSpan.FromMilliseconds(
                    (existing.AverageResponseTime.TotalMilliseconds + responseTime.TotalMilliseconds) / 2);
                
                existing.LastUsed = DateTime.UtcNow;
                existing.IsHealthy = success;
                if (!success && !string.IsNullOrEmpty(error))
                    existing.LastError = error;
                
                return existing;
            });
    }
}
