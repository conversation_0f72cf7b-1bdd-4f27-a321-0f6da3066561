# 🚀 SMS Gateway - Enterprise SMS Platform

A comprehensive, enterprise-ready SMS Gateway system built with .NET 9, featuring multi-tenancy, plugin architecture, templating, queuing, and a modern Blazor UI portal.

## 🌟 Features

### 🏗️ **Architecture**
- **Plugin-Based Providers**: Runtime loading/unloading of SMS provider plugins
- **Multi-Tenancy**: Complete tenant isolation with Finbuckle.MultiTenant
- **Microservices Ready**: Separated concerns with dedicated projects
- **Database Abstraction**: Repository pattern with Entity Framework Core

### 📱 **SMS Capabilities**
- **Multiple Providers**: Twilio, BulkSMS, Mock (extensible)
- **Smart Routing**: Auto-failover and provider selection
- **Bulk Messaging**: Batch processing with background jobs
- **Message Scheduling**: Queue messages for future delivery
- **Delivery Tracking**: Real-time status updates and webhooks

### 🎨 **Templating System**
- **Liquid Templates**: Powerful template engine with variable substitution
- **Template Management**: Create, edit, test, and version templates
- **Variable Validation**: Ensure required variables are provided
- **Preview System**: Real-time template rendering preview

### ⚡ **Background Processing**
- **Hangfire Integration**: Reliable background job processing
- **Redis Support**: Distributed caching and session storage
- **Queue Management**: Priority-based message queuing
- **Retry Logic**: Configurable retry mechanisms

### 🔐 **Security & Authentication**
- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control**: Granular permissions system
- **API Key Support**: Programmatic access with API keys
- **Multi-Factor Authentication**: Enhanced security options

### 📊 **Monitoring & Analytics**
- **Real-time Dashboard**: Live statistics and metrics
- **Provider Health Checks**: Monitor provider availability
- **Usage Analytics**: Detailed reporting and insights
- **Audit Logging**: Complete audit trail

### 🎯 **Modern UI Portal**
- **Blazor Server**: Modern, responsive web interface
- **MudBlazor Components**: Material Design components
- **Real-time Updates**: Live dashboard and notifications
- **Mobile Responsive**: Works on all devices

## 🏗️ Project Structure

```
SmsGateway/
├── SmsGateway.Core/              # Core interfaces, models, authentication
├── SmsGateway.Database/          # Entity Framework, repositories
├── SmsGateway.Services/          # Business logic services
├── SmsGateway.Api/               # REST API with Swagger
├── SmsGateway.Portal/            # Blazor UI Portal
├── SmsGateway.Providers.Mock/    # Mock provider plugin
├── SmsGateway.Providers.Twilio/  # Twilio provider plugin
└── SmsGateway.Providers.BulkSms/ # BulkSMS provider plugin
```

## 🚀 Quick Start

### Prerequisites
- .NET 9 SDK
- SQL Server (LocalDB for development)
- Redis (optional, for distributed scenarios)

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/sms-gateway.git
cd sms-gateway
```

### 2. Configure Settings
Update `appsettings.json` in both API and Portal projects:

**API Configuration:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=SmsGateway;Trusted_Connection=true;"
  },
  "SmsProviders": {
    "Mock": {
      "Enabled": true,
      "Priority": 999
    },
    "Twilio": {
      "Enabled": false,
      "Username": "your_account_sid",
      "Password": "your_auth_token"
    },
    "BulkSMS": {
      "Enabled": false,
      "Username": "your_username",
      "Password": "your_password"
    }
  }
}
```

### 3. Run Database Migrations
```bash
cd SmsGateway.Api
dotnet ef database update
```

### 4. Start the Services
```bash
# Terminal 1 - API
cd SmsGateway.Api
dotnet run

# Terminal 2 - Portal
cd SmsGateway.Portal
dotnet run
```

### 5. Access the Applications
- **API**: https://localhost:7001
- **Swagger UI**: https://localhost:7001/swagger
- **Portal**: https://localhost:7002

## 📖 API Documentation

### Authentication
```bash
# Login
POST /api/auth/login
{
  "username": "admin",
  "password": "password"
}

# Use JWT token in subsequent requests
Authorization: Bearer <token>
```

### Send SMS
```bash
# Send single SMS
POST /api/sms/send
{
  "to": "+**********",
  "message": "Hello from SMS Gateway!",
  "provider": "Twilio"
}

# Send with template
POST /api/sms/send
{
  "to": "+**********",
  "templateId": "guid-here",
  "templateVariables": {
    "name": "John",
    "code": "123456"
  }
}
```

### Template Management
```bash
# Create template
POST /api/templates
{
  "name": "welcome",
  "displayName": "Welcome Message",
  "content": "Welcome {{name}}! Your verification code is {{code}}."
}
```

## 🔌 Creating Custom Providers

### 1. Create Provider Class
```csharp
public class CustomSmsProvider : ISmsProvider
{
    public string Name => "Custom";
    public string DisplayName => "Custom SMS Provider";
    public string Version => "1.0.0";
    public string Description => "Custom SMS provider implementation";
    public bool IsAvailable => true;

    public async Task<SmsResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        // Implementation here
    }
    
    // Implement other interface methods...
}
```

### 2. Create Plugin Factory
```csharp
public class CustomPluginFactory : PluginFactoryBase
{
    public override PluginMetadata Metadata => new()
    {
        AssemblyName = "SmsGateway.Providers.Custom",
        Version = "1.0.0",
        Author = "Your Name",
        Description = "Custom SMS provider"
    };

    public override IEnumerable<ISmsProvider> CreateProviders()
    {
        yield return new CustomSmsProvider();
    }
}
```

### 3. Build and Deploy
```bash
dotnet build
# Copy DLL to API/Plugins folder
# Provider will be auto-loaded at runtime
```

## 🎨 Portal Features

### Dashboard
- Real-time message statistics
- Provider health status
- Recent message history
- Quick action buttons

### Send SMS
- Single and bulk messaging
- Template selection
- Message preview
- Scheduling options

### Template Management
- Visual template editor
- Variable management
- Template testing
- Usage analytics

### Provider Management
- Provider configuration
- Health monitoring
- Plugin management
- Statistics and analytics

### Administration
- User management
- Role assignment
- Tenant management
- System settings

## 🔧 Configuration

### Provider Configuration
```json
{
  "SmsProviders": {
    "ProviderName": {
      "Type": "apikey|usernamepassword|basic",
      "Enabled": true,
      "Priority": 1,
      "MaxRetries": 3,
      "TimeoutSeconds": 30,
      "RateLimitPerMinute": 100,
      "DefaultSenderId": "YourBrand",
      "SupportedCountries": ["US", "CA", "GB"],
      "Settings": {
        "CustomSetting": "value"
      }
    }
  }
}
```

### Multi-Tenancy
```json
{
  "MultiTenant": {
    "Stores": {
      "ConfigurationStore": {
        "Tenants": {
          "tenant1": {
            "Id": "tenant1",
            "Identifier": "tenant1",
            "Name": "Tenant One",
            "ConnectionString": "..."
          }
        }
      }
    }
  }
}
```

## 🚀 Deployment

### Docker Support
```dockerfile
# API Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0
COPY . /app
WORKDIR /app
EXPOSE 80
ENTRYPOINT ["dotnet", "SmsGateway.Api.dll"]
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sms-gateway-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sms-gateway-api
  template:
    metadata:
      labels:
        app: sms-gateway-api
    spec:
      containers:
      - name: api
        image: sms-gateway-api:latest
        ports:
        - containerPort: 80
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](https://github.com/your-org/sms-gateway/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-org/sms-gateway/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/sms-gateway/discussions)

## 🎯 Roadmap

- [ ] Additional provider plugins (SendGrid, Amazon SNS, etc.)
- [ ] GraphQL API support
- [ ] Advanced analytics and reporting
- [ ] Mobile app for monitoring
- [ ] Webhook management UI
- [ ] A/B testing for messages
- [ ] Message personalization AI

---

**Built with ❤️ using .NET 9, Blazor, and modern web technologies.**
