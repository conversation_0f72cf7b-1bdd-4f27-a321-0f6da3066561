using System.Diagnostics;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models;

namespace SmsGateway.Providers.Mock;

/// <summary>
/// Mock SMS provider for testing and development
/// </summary>
public class MockSmsProvider : ISmsProvider
{
    private readonly ILogger<MockSmsProvider>? _logger;
    private ProviderConfiguration? _configuration;
    private readonly Dictionary<string, SmsStatus> _messageStatuses = new();
    private readonly Random _random = new();

    public string Name => "Mock";
    public string DisplayName => "Mock SMS Provider";
    public string Version => "1.0.0";
    public string Description => "Mock SMS provider for testing and development purposes";
    public bool IsAvailable => _configuration?.Enabled == true;

    public MockSmsProvider(ILogger<MockSmsProvider>? logger = null)
    {
        _logger = logger;
    }

    public async Task InitializeAsync(ProviderConfiguration configuration)
    {
        _configuration = configuration;
        _logger?.LogInformation("Mock SMS provider initialized");
        await Task.CompletedTask;
    }

    public async Task<SmsResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        if (_configuration == null)
        {
            throw new InvalidOperationException("Provider not initialized");
        }

        // Simulate network delay
        await Task.Delay(_random.Next(100, 500), cancellationToken);

        var messageId = Guid.NewGuid().ToString();
        
        // Simulate different outcomes based on phone number patterns
        var success = !request.To.EndsWith("0000"); // Numbers ending in 0000 will fail
        var status = success ? SmsStatus.Sent : SmsStatus.Failed;
        
        // Store status for later retrieval
        _messageStatuses[messageId] = status;
        
        // Simulate status progression for successful messages
        if (success)
        {
            _ = Task.Run(async () =>
            {
                await Task.Delay(2000, cancellationToken);
                if (_messageStatuses.ContainsKey(messageId))
                    _messageStatuses[messageId] = SmsStatus.Delivered;
            }, cancellationToken);
        }

        var response = new SmsResponse
        {
            Success = success,
            MessageId = messageId,
            Status = status,
            Cost = 0.01m,
            Currency = "USD",
            Segments = CalculateSegments(request.Message)
        };

        if (!success)
        {
            response.ErrorMessage = "Mock failure for testing";
            response.ErrorCode = "MOCK_ERROR";
        }

        _logger?.LogInformation("Mock SMS {Status} - To: {To}, MessageId: {MessageId}", 
            success ? "sent" : "failed", request.To, messageId);

        return response;
    }

    public async Task<SmsStatus> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        await Task.Delay(50, cancellationToken); // Simulate API call
        
        return _messageStatuses.TryGetValue(messageId, out var status) ? status : SmsStatus.Unknown;
    }

    public bool SupportsCountry(string countryCode)
    {
        // Mock provider supports all countries
        return _configuration?.SupportedCountries.Count == 0 || 
               _configuration?.SupportedCountries.Contains(countryCode, StringComparer.OrdinalIgnoreCase) == true;
    }

    public async Task<(decimal cost, string currency)?> GetEstimatedCostAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        await Task.Delay(50, cancellationToken);
        
        var segments = CalculateSegments(request.Message);
        var costPerSegment = 0.01m;
        
        return (segments * costPerSegment, "USD");
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(ProviderConfiguration configuration)
    {
        await Task.CompletedTask;
        
        return new ValidationResult
        {
            IsValid = true,
            Warnings = { "This is a mock provider for testing only" }
        };
    }

    public async Task<HealthCheckResult> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        // Simulate health check delay
        await Task.Delay(50, cancellationToken);
        
        stopwatch.Stop();

        // Simulate occasional health check failures
        var isHealthy = _random.Next(1, 101) > 5; // 95% success rate

        return new HealthCheckResult
        {
            IsHealthy = isHealthy,
            Message = isHealthy ? "Mock provider is healthy" : "Mock provider simulated failure",
            ResponseTime = stopwatch.Elapsed,
            Data = new Dictionary<string, object>
            {
                { "TotalMessagesSent", _messageStatuses.Count },
                { "SuccessfulMessages", _messageStatuses.Values.Count(s => s != SmsStatus.Failed) },
                { "FailedMessages", _messageStatuses.Values.Count(s => s == SmsStatus.Failed) }
            }
        };
    }

    public async Task DisposeAsync()
    {
        _messageStatuses.Clear();
        _logger?.LogInformation("Mock SMS provider disposed");
        await Task.CompletedTask;
    }

    private int CalculateSegments(string message)
    {
        // SMS segments are typically 160 characters for GSM 7-bit encoding
        // or 70 characters for UCS-2 encoding (Unicode)
        var maxLength = ContainsUnicodeCharacters(message) ? 70 : 160;
        return (int)Math.Ceiling((double)message.Length / maxLength);
    }

    private bool ContainsUnicodeCharacters(string text)
    {
        // Simple check for non-ASCII characters
        return text.Any(c => c > 127);
    }

    /// <summary>
    /// Get current message statuses (for testing/debugging)
    /// </summary>
    public Dictionary<string, SmsStatus> GetMessageStatuses()
    {
        return new Dictionary<string, SmsStatus>(_messageStatuses);
    }

    /// <summary>
    /// Clear message statuses (for testing)
    /// </summary>
    public void ClearMessageStatuses()
    {
        _messageStatuses.Clear();
    }

    /// <summary>
    /// Simulate status update for a message (for testing)
    /// </summary>
    public void UpdateMessageStatus(string messageId, SmsStatus status)
    {
        if (_messageStatuses.ContainsKey(messageId))
        {
            _messageStatuses[messageId] = status;
            _logger?.LogInformation("Updated mock message {MessageId} status to {Status}", messageId, status);
        }
    }
}
