using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models;

namespace SmsGateway.Providers.Mock;

/// <summary>
/// Plugin factory for Mock SMS provider
/// </summary>
public class MockPluginFactory : PluginFactoryBase
{
    public override PluginMetadata Metadata => new()
    {
        AssemblyName = "SmsGateway.Providers.Mock",
        Version = "1.0.0",
        Author = "SMS Gateway Team",
        Description = "Mock SMS provider for testing and development",
        Tags = { "mock", "testing", "development" },
        MinimumCoreVersion = "1.0.0"
    };

    public override IEnumerable<ISmsProvider> CreateProviders()
    {
        // Create logger if available (will be null in plugin context)
        ILogger<MockSmsProvider>? logger = null;
        
        yield return new MockSmsProvider(logger);
    }

    public override IEnumerable<Type> GetSupportedConfigurationTypes()
    {
        return new[] { typeof(ProviderConfiguration) };
    }

    public override ValidationResult ValidateDependencies()
    {
        // Mock provider has no external dependencies
        return new ValidationResult { IsValid = true };
    }
}
