<?xml version="1.0" encoding="utf-8"?>
<RuleSet Name="SMS Gateway Rules" Description="Code analysis rules for SMS Gateway projects" ToolsVersion="16.0">
  <Localization ResourceAssembly="Microsoft.VisualStudio.CodeAnalysis.RuleSets.Strings.dll" ResourceBaseName="Microsoft.VisualStudio.CodeAnalysis.RuleSets.Strings.Localized">
    <Name Resource="MinimumRecommendedRules_Name" />
    <Description Resource="MinimumRecommendedRules_Description" />
  </Localization>
  
  <!-- Design Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1000" Action="Warning" />  <!-- Do not declare static members on generic types -->
    <Rule Id="CA1001" Action="Warning" />  <!-- Types that own disposable fields should be disposable -->
    <Rule Id="CA1002" Action="Warning" />  <!-- Do not expose generic lists -->
    <Rule Id="CA1003" Action="Warning" />  <!-- Use generic event handler instances -->
    <Rule Id="CA1005" Action="Warning" />  <!-- Avoid excessive parameters on generic types -->
    <Rule Id="CA1008" Action="Warning" />  <!-- Enums should have zero value -->
    <Rule Id="CA1010" Action="Warning" />  <!-- Collections should implement generic interface -->
    <Rule Id="CA1012" Action="Warning" />  <!-- Abstract types should not have constructors -->
    <Rule Id="CA1014" Action="Warning" />  <!-- Mark assemblies with CLSCompliant -->
    <Rule Id="CA1016" Action="Warning" />  <!-- Mark assemblies with assembly version -->
    <Rule Id="CA1017" Action="Warning" />  <!-- Mark assemblies with ComVisible -->
    <Rule Id="CA1018" Action="Warning" />  <!-- Mark attributes with AttributeUsageAttribute -->
    <Rule Id="CA1019" Action="Warning" />  <!-- Define accessors for attribute arguments -->
    <Rule Id="CA1024" Action="Warning" />  <!-- Use properties where appropriate -->
    <Rule Id="CA1027" Action="Warning" />  <!-- Mark enums with FlagsAttribute -->
    <Rule Id="CA1028" Action="Warning" />  <!-- Enum Storage should be Int32 -->
    <Rule Id="CA1030" Action="Warning" />  <!-- Use events where appropriate -->
    <Rule Id="CA1031" Action="None" />     <!-- Do not catch general exception types -->
    <Rule Id="CA1032" Action="Warning" />  <!-- Implement standard exception constructors -->
    <Rule Id="CA1033" Action="Warning" />  <!-- Interface methods should be callable by child types -->
    <Rule Id="CA1034" Action="Warning" />  <!-- Nested types should not be visible -->
    <Rule Id="CA1036" Action="Warning" />  <!-- Override methods on comparable types -->
    <Rule Id="CA1040" Action="Warning" />  <!-- Avoid empty interfaces -->
    <Rule Id="CA1041" Action="Warning" />  <!-- Provide ObsoleteAttribute message -->
    <Rule Id="CA1043" Action="Warning" />  <!-- Use Integral Or String Argument For Indexers -->
    <Rule Id="CA1044" Action="Warning" />  <!-- Properties should not be write only -->
    <Rule Id="CA1046" Action="Warning" />  <!-- Do not overload operator equals on reference types -->
    <Rule Id="CA1047" Action="Warning" />  <!-- Do not declare protected member in sealed type -->
    <Rule Id="CA1048" Action="Warning" />  <!-- Do not declare virtual members in sealed type -->
    <Rule Id="CA1050" Action="Warning" />  <!-- Declare types in namespaces -->
    <Rule Id="CA1051" Action="Warning" />  <!-- Do not declare visible instance fields -->
    <Rule Id="CA1052" Action="Warning" />  <!-- Static holder types should be Static or NotInheritable -->
    <Rule Id="CA1054" Action="Warning" />  <!-- Uri parameters should not be strings -->
    <Rule Id="CA1055" Action="Warning" />  <!-- Uri return values should not be strings -->
    <Rule Id="CA1056" Action="Warning" />  <!-- Uri properties should not be strings -->
    <Rule Id="CA1058" Action="Warning" />  <!-- Types should not extend certain base types -->
    <Rule Id="CA1060" Action="Warning" />  <!-- Move pinvokes to native methods class -->
    <Rule Id="CA1061" Action="Warning" />  <!-- Do not hide base class methods -->
    <Rule Id="CA1063" Action="Warning" />  <!-- Implement IDisposable Correctly -->
    <Rule Id="CA1064" Action="Warning" />  <!-- Exceptions should be public -->
    <Rule Id="CA1065" Action="Warning" />  <!-- Do not raise exceptions in unexpected locations -->
  </Rules>

  <!-- Globalization Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1300" Action="Warning" />  <!-- Specify MessageBoxOptions -->
    <Rule Id="CA1301" Action="Warning" />  <!-- Avoid duplicate accelerators -->
    <Rule Id="CA1302" Action="Warning" />  <!-- Do not hardcode locale specific strings -->
    <Rule Id="CA1303" Action="None" />     <!-- Do not pass literals as localized parameters -->
    <Rule Id="CA1304" Action="Warning" />  <!-- Specify CultureInfo -->
    <Rule Id="CA1305" Action="Warning" />  <!-- Specify IFormatProvider -->
    <Rule Id="CA1306" Action="Warning" />  <!-- Set locale for data types -->
    <Rule Id="CA1307" Action="Warning" />  <!-- Specify StringComparison -->
    <Rule Id="CA1308" Action="Warning" />  <!-- Normalize strings to uppercase -->
    <Rule Id="CA1309" Action="Warning" />  <!-- Use ordinal stringcomparison -->
  </Rules>

  <!-- Interoperability Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1400" Action="Warning" />  <!-- P/Invoke entry points should exist -->
    <Rule Id="CA1401" Action="Warning" />  <!-- P/Invokes should not be visible -->
    <Rule Id="CA1402" Action="Warning" />  <!-- Avoid overloads in COM visible interfaces -->
    <Rule Id="CA1403" Action="Warning" />  <!-- Auto layout types should not be COM visible -->
    <Rule Id="CA1404" Action="Warning" />  <!-- Call GetLastError immediately after P/Invoke -->
    <Rule Id="CA1405" Action="Warning" />  <!-- COM visible type base types should be COM visible -->
    <Rule Id="CA1410" Action="Warning" />  <!-- COM registration methods should be matched -->
    <Rule Id="CA1411" Action="Warning" />  <!-- COM registration methods should not be visible -->
    <Rule Id="CA1412" Action="Warning" />  <!-- Mark ComSource Interfaces as IDispatch -->
    <Rule Id="CA1413" Action="Warning" />  <!-- Avoid non-public fields in COM visible value types -->
    <Rule Id="CA1414" Action="Warning" />  <!-- Mark boolean P/Invoke arguments with MarshalAs -->
    <Rule Id="CA1415" Action="Warning" />  <!-- Declare P/Invokes correctly -->
  </Rules>

  <!-- Maintainability Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1500" Action="Warning" />  <!-- Variable names should not match field names -->
    <Rule Id="CA1501" Action="Warning" />  <!-- Avoid excessive inheritance -->
    <Rule Id="CA1502" Action="Warning" />  <!-- Avoid excessive complexity -->
    <Rule Id="CA1504" Action="Warning" />  <!-- Review misleading field names -->
    <Rule Id="CA1505" Action="Warning" />  <!-- Avoid unmaintainable code -->
    <Rule Id="CA1506" Action="Warning" />  <!-- Avoid excessive class coupling -->
  </Rules>

  <!-- Mobility Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1600" Action="Warning" />  <!-- Do not use idle process priority -->
    <Rule Id="CA1601" Action="Warning" />  <!-- Do not use timers that prevent power state changes -->
  </Rules>

  <!-- Naming Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1700" Action="Warning" />  <!-- Do not name enum values 'Reserved' -->
    <Rule Id="CA1701" Action="Warning" />  <!-- Resource string compound words should be cased correctly -->
    <Rule Id="CA1702" Action="Warning" />  <!-- Compound words should be cased correctly -->
    <Rule Id="CA1703" Action="Warning" />  <!-- Resource strings should be spelled correctly -->
    <Rule Id="CA1704" Action="Warning" />  <!-- Identifiers should be spelled correctly -->
    <Rule Id="CA1707" Action="Warning" />  <!-- Identifiers should not contain underscores -->
    <Rule Id="CA1708" Action="Warning" />  <!-- Identifiers should differ by more than case -->
    <Rule Id="CA1709" Action="Warning" />  <!-- Identifiers should be cased correctly -->
    <Rule Id="CA1710" Action="Warning" />  <!-- Identifiers should have correct suffix -->
    <Rule Id="CA1711" Action="Warning" />  <!-- Identifiers should not have incorrect suffix -->
    <Rule Id="CA1712" Action="Warning" />  <!-- Do not prefix enum values with type name -->
    <Rule Id="CA1713" Action="Warning" />  <!-- Events should not have 'Before' or 'After' prefix -->
    <Rule Id="CA1714" Action="Warning" />  <!-- Flags enums should have plural names -->
    <Rule Id="CA1715" Action="Warning" />  <!-- Identifiers should have correct prefix -->
    <Rule Id="CA1716" Action="Warning" />  <!-- Identifiers should not match keywords -->
    <Rule Id="CA1717" Action="Warning" />  <!-- Only FlagsAttribute enums should have plural names -->
    <Rule Id="CA1719" Action="Warning" />  <!-- Parameter names should not match member names -->
    <Rule Id="CA1720" Action="Warning" />  <!-- Identifier contains type name -->
    <Rule Id="CA1721" Action="Warning" />  <!-- Property names should not match get methods -->
    <Rule Id="CA1722" Action="Warning" />  <!-- Identifiers should not have incorrect prefix -->
    <Rule Id="CA1724" Action="Warning" />  <!-- Type names should not match namespaces -->
    <Rule Id="CA1725" Action="Warning" />  <!-- Parameter names should match base declaration -->
    <Rule Id="CA1726" Action="Warning" />  <!-- Use preferred terms -->
  </Rules>

  <!-- Performance Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1800" Action="Warning" />  <!-- Do not cast unnecessarily -->
    <Rule Id="CA1801" Action="Warning" />  <!-- Review unused parameters -->
    <Rule Id="CA1802" Action="Warning" />  <!-- Use literals where appropriate -->
    <Rule Id="CA1804" Action="Warning" />  <!-- Remove unused locals -->
    <Rule Id="CA1806" Action="Warning" />  <!-- Do not ignore method results -->
    <Rule Id="CA1809" Action="Warning" />  <!-- Avoid excessive locals -->
    <Rule Id="CA1810" Action="Warning" />  <!-- Initialize reference type static fields inline -->
    <Rule Id="CA1811" Action="Warning" />  <!-- Avoid uncalled private code -->
    <Rule Id="CA1812" Action="Warning" />  <!-- Avoid uninstantiated internal classes -->
    <Rule Id="CA1813" Action="Warning" />  <!-- Avoid unsealed attributes -->
    <Rule Id="CA1814" Action="Warning" />  <!-- Prefer jagged arrays over multidimensional -->
    <Rule Id="CA1815" Action="Warning" />  <!-- Override equals and operator equals on value types -->
    <Rule Id="CA1816" Action="Warning" />  <!-- Dispose methods should call SuppressFinalize -->
    <Rule Id="CA1819" Action="Warning" />  <!-- Properties should not return arrays -->
    <Rule Id="CA1820" Action="Warning" />  <!-- Test for empty strings using string length -->
    <Rule Id="CA1821" Action="Warning" />  <!-- Remove empty Finalizers -->
    <Rule Id="CA1822" Action="Warning" />  <!-- Mark members as static -->
    <Rule Id="CA1823" Action="Warning" />  <!-- Avoid unused private fields -->
    <Rule Id="CA1824" Action="Warning" />  <!-- Mark assemblies with NeutralResourcesLanguageAttribute -->
  </Rules>

  <!-- Portability Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1900" Action="Warning" />  <!-- Value type fields should be portable -->
    <Rule Id="CA1901" Action="Warning" />  <!-- P/Invoke declarations should be portable -->
  </Rules>

  <!-- Reliability Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA2000" Action="Warning" />  <!-- Dispose objects before losing scope -->
    <Rule Id="CA2001" Action="Warning" />  <!-- Avoid calling problematic methods -->
    <Rule Id="CA2002" Action="Warning" />  <!-- Do not lock on objects with weak identity -->
    <Rule Id="CA2003" Action="Warning" />  <!-- Do not treat fibers as threads -->
    <Rule Id="CA2004" Action="Warning" />  <!-- Remove calls to GC.KeepAlive -->
    <Rule Id="CA2006" Action="Warning" />  <!-- Use SafeHandle to encapsulate native resources -->
  </Rules>

  <!-- Security Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA2100" Action="Warning" />  <!-- Review SQL queries for security vulnerabilities -->
    <Rule Id="CA2101" Action="Warning" />  <!-- Specify marshaling for P/Invoke string arguments -->
    <Rule Id="CA2102" Action="Warning" />  <!-- Catch non-CLSCompliant exceptions in general handlers -->
    <Rule Id="CA2103" Action="Warning" />  <!-- Review imperative security -->
    <Rule Id="CA2104" Action="Warning" />  <!-- Do not declare read only mutable reference types -->
    <Rule Id="CA2105" Action="Warning" />  <!-- Array fields should not be read only -->
    <Rule Id="CA2106" Action="Warning" />  <!-- Secure asserts -->
    <Rule Id="CA2107" Action="Warning" />  <!-- Review deny and permit only usage -->
    <Rule Id="CA2108" Action="Warning" />  <!-- Review declarative security on value types -->
    <Rule Id="CA2109" Action="Warning" />  <!-- Review visible event handlers -->
    <Rule Id="CA2111" Action="Warning" />  <!-- Pointers should not be visible -->
    <Rule Id="CA2112" Action="Warning" />  <!-- Secured types should not expose fields -->
    <Rule Id="CA2114" Action="Warning" />  <!-- Method security should be a superset of type -->
    <Rule Id="CA2115" Action="Warning" />  <!-- Call GC.KeepAlive when using native resources -->
    <Rule Id="CA2116" Action="Warning" />  <!-- APTCA methods should only call APTCA methods -->
    <Rule Id="CA2117" Action="Warning" />  <!-- APTCA types should only extend APTCA base types -->
    <Rule Id="CA2118" Action="Warning" />  <!-- Review SuppressUnmanagedCodeSecurityAttribute usage -->
    <Rule Id="CA2119" Action="Warning" />  <!-- Seal methods that satisfy private interfaces -->
    <Rule Id="CA2120" Action="Warning" />  <!-- Secure serialization constructors -->
    <Rule Id="CA2121" Action="Warning" />  <!-- Static constructors should be private -->
    <Rule Id="CA2122" Action="Warning" />  <!-- Do not indirectly expose methods with link demands -->
    <Rule Id="CA2123" Action="Warning" />  <!-- Override link demands should be identical to base -->
    <Rule Id="CA2124" Action="Warning" />  <!-- Wrap vulnerable finally clauses in outer try -->
    <Rule Id="CA2126" Action="Warning" />  <!-- Type link demands require inheritance demands -->
    <Rule Id="CA2130" Action="Warning" />  <!-- Security critical constants should be transparent -->
    <Rule Id="CA2131" Action="Warning" />  <!-- Security critical types may not participate in type equivalence -->
    <Rule Id="CA2132" Action="Warning" />  <!-- Default constructors must be at least as critical as base type default constructors -->
    <Rule Id="CA2133" Action="Warning" />  <!-- Delegates must bind to methods with consistent transparency -->
    <Rule Id="CA2134" Action="Warning" />  <!-- Methods must keep consistent transparency when overriding base methods -->
    <Rule Id="CA2135" Action="Warning" />  <!-- Level 2 assemblies should not contain LinkDemands -->
    <Rule Id="CA2136" Action="Warning" />  <!-- Members should not have conflicting transparency annotations -->
    <Rule Id="CA2137" Action="Warning" />  <!-- Transparent methods must contain only verifiable IL -->
    <Rule Id="CA2138" Action="Warning" />  <!-- Transparent methods must not call methods with the SuppressUnmanagedCodeSecurity attribute -->
    <Rule Id="CA2139" Action="Warning" />  <!-- Transparent methods may not use the HandleProcessCorruptingExceptions attribute -->
    <Rule Id="CA2140" Action="Warning" />  <!-- Transparent code must not reference security critical items -->
    <Rule Id="CA2141" Action="Warning" />  <!-- Transparent methods must not satisfy LinkDemands -->
    <Rule Id="CA2142" Action="Warning" />  <!-- Transparent code should not be protected with LinkDemands -->
    <Rule Id="CA2143" Action="Warning" />  <!-- Transparent methods should not use security demands -->
    <Rule Id="CA2144" Action="Warning" />  <!-- Transparent code should not load assemblies from byte arrays -->
    <Rule Id="CA2145" Action="Warning" />  <!-- Transparent methods should not be decorated with the SuppressUnmanagedCodeSecurityAttribute -->
    <Rule Id="CA2146" Action="Warning" />  <!-- Types must be at least as critical as their base types and interfaces -->
    <Rule Id="CA2147" Action="Warning" />  <!-- Transparent methods may not use security asserts -->
    <Rule Id="CA2149" Action="Warning" />  <!-- Transparent methods must not call into native code -->
    <Rule Id="CA2151" Action="Warning" />  <!-- Fields with critical types should be security critical -->
  </Rules>

  <!-- Usage Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA2200" Action="Warning" />  <!-- Rethrow to preserve stack details -->
    <Rule Id="CA2201" Action="Warning" />  <!-- Do not raise reserved exception types -->
    <Rule Id="CA2202" Action="Warning" />  <!-- Do not dispose objects multiple times -->
    <Rule Id="CA2204" Action="Warning" />  <!-- Literals should be spelled correctly -->
    <Rule Id="CA2205" Action="Warning" />  <!-- Use managed equivalents of win32 api -->
    <Rule Id="CA2207" Action="Warning" />  <!-- Initialize value type static fields inline -->
    <Rule Id="CA2208" Action="Warning" />  <!-- Instantiate argument exceptions correctly -->
    <Rule Id="CA2210" Action="Warning" />  <!-- Assemblies should have valid strong names -->
    <Rule Id="CA2211" Action="Warning" />  <!-- Non-constant fields should not be visible -->
    <Rule Id="CA2212" Action="Warning" />  <!-- Do not mark serviced components with WebMethod -->
    <Rule Id="CA2213" Action="Warning" />  <!-- Disposable fields should be disposed -->
    <Rule Id="CA2214" Action="Warning" />  <!-- Do not call overridable methods in constructors -->
    <Rule Id="CA2215" Action="Warning" />  <!-- Dispose methods should call base class dispose -->
    <Rule Id="CA2216" Action="Warning" />  <!-- Disposable types should declare finalizer -->
    <Rule Id="CA2217" Action="Warning" />  <!-- Do not mark enums with FlagsAttribute -->
    <Rule Id="CA2218" Action="Warning" />  <!-- Override GetHashCode on overriding Equals -->
    <Rule Id="CA2219" Action="Warning" />  <!-- Do not raise exceptions in exception clauses -->
    <Rule Id="CA2220" Action="Warning" />  <!-- Finalizers should call base class finalizer -->
    <Rule Id="CA2221" Action="Warning" />  <!-- Finalizers should be protected -->
    <Rule Id="CA2222" Action="Warning" />  <!-- Do not decrease inherited member visibility -->
    <Rule Id="CA2223" Action="Warning" />  <!-- Members should differ by more than return type -->
    <Rule Id="CA2224" Action="Warning" />  <!-- Override Equals on overloading operator equals -->
    <Rule Id="CA2225" Action="Warning" />  <!-- Operator overloads have named alternates -->
    <Rule Id="CA2226" Action="Warning" />  <!-- Operators should have symmetrical overloads -->
    <Rule Id="CA2227" Action="Warning" />  <!-- Collection properties should be read only -->
    <Rule Id="CA2228" Action="Warning" />  <!-- Do not ship unreleased resource formats -->
    <Rule Id="CA2229" Action="Warning" />  <!-- Implement serialization constructors -->
    <Rule Id="CA2230" Action="Warning" />  <!-- Use params for variable arguments -->
    <Rule Id="CA2231" Action="Warning" />  <!-- Overload operator equals on overriding value type Equals -->
    <Rule Id="CA2232" Action="Warning" />  <!-- Mark Windows Forms entry points with STAThread -->
    <Rule Id="CA2233" Action="Warning" />  <!-- Operations should not overflow -->
    <Rule Id="CA2234" Action="Warning" />  <!-- Pass system uri objects instead of strings -->
    <Rule Id="CA2235" Action="Warning" />  <!-- Mark all non-serializable fields -->
    <Rule Id="CA2236" Action="Warning" />  <!-- Call base class methods on ISerializable types -->
    <Rule Id="CA2237" Action="Warning" />  <!-- Mark ISerializable types with serializable -->
    <Rule Id="CA2238" Action="Warning" />  <!-- Implement serialization methods correctly -->
    <Rule Id="CA2239" Action="Warning" />  <!-- Provide deserialization methods for optional fields -->
    <Rule Id="CA2240" Action="Warning" />  <!-- Implement ISerializable correctly -->
    <Rule Id="CA2241" Action="Warning" />  <!-- Provide correct arguments to formatting methods -->
    <Rule Id="CA2242" Action="Warning" />  <!-- Test for NaN correctly -->
    <Rule Id="CA2243" Action="Warning" />  <!-- Attribute string literals should parse correctly -->
  </Rules>
</RuleSet>
