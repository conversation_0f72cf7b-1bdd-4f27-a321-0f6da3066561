<Project>

  <!-- Common package references for all projects -->
  <ItemGroup>
    <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All" />
  </ItemGroup>

  <!-- Development dependencies -->
  <ItemGroup Condition="'$(Configuration)' == 'Debug'">
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="$(MicrosoftExtensionsVersion)" />
  </ItemGroup>

  <!-- Common files to include in packages -->
  <ItemGroup Condition="'$(IsPackable)' == 'true'">
    <None Include="$(MSBuildThisFileDirectory)../README.md" Pack="true" PackagePath="\" />
    <None Include="$(MSBuildThisFileDirectory)../LICENSE" Pack="true" PackagePath="\" Condition="Exists('$(MSBuildThisFileDirectory)../LICENSE')" />
  </ItemGroup>

  <!-- Code analysis -->
  <ItemGroup Condition="'$(EnableCodeAnalysis)' != 'false'">
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4" PrivateAssets="All" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="8.0.0" PrivateAssets="All" />
  </ItemGroup>

  <!-- Security analysis -->
  <ItemGroup Condition="'$(EnableSecurityAnalysis)' != 'false'">
    <PackageReference Include="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="3.3.4" PrivateAssets="All" />
    <PackageReference Include="SecurityCodeScan.VS2019" Version="5.6.7" PrivateAssets="All" />
  </ItemGroup>

  <!-- Test projects -->
  <ItemGroup Condition="'$(IsTestProject)' == 'true'">
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3" PrivateAssets="All" />
    <PackageReference Include="coverlet.collector" Version="6.0.0" PrivateAssets="All" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="$(MicrosoftAspNetCoreVersion)" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="$(MicrosoftEntityFrameworkCoreVersion)" />
  </ItemGroup>

  <!-- Web projects -->
  <ItemGroup Condition="'$(IsWebProject)' == 'true'">
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="$(MicrosoftAspNetCoreVersion)" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="$(SwashbuckleVersion)" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="$(SwashbuckleVersion)" />
    <PackageReference Include="Serilog.AspNetCore" Version="$(SerilogVersion)" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="$(MicrosoftAspNetCoreVersion)" />
  </ItemGroup>

  <!-- Database projects -->
  <ItemGroup Condition="'$(IsDatabaseProject)' == 'true'">
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="$(MicrosoftEntityFrameworkCoreVersion)" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="$(MicrosoftEntityFrameworkCoreVersion)" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="$(MicrosoftEntityFrameworkCoreVersion)" />
    <PackageReference Include="Finbuckle.MultiTenant.EntityFrameworkCore" Version="$(FinbuckleMultiTenantVersion)" />
  </ItemGroup>

  <!-- Service projects -->
  <ItemGroup Condition="'$(IsServiceProject)' == 'true'">
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="$(MicrosoftExtensionsVersion)" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="$(MicrosoftExtensionsVersion)" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="$(MicrosoftExtensionsVersion)" />
  </ItemGroup>

  <!-- API projects -->
  <ItemGroup Condition="'$(IsApiProject)' == 'true'">
    <PackageReference Include="Hangfire.Core" Version="$(HangfireVersion)" />
    <PackageReference Include="Hangfire.SqlServer" Version="$(HangfireVersion)" />
    <PackageReference Include="Hangfire.AspNetCore" Version="$(HangfireVersion)" />
    <PackageReference Include="StackExchange.Redis" Version="$(StackExchangeRedisVersion)" />
    <PackageReference Include="DotLiquid" Version="$(DotLiquidVersion)" />
    <PackageReference Include="Finbuckle.MultiTenant" Version="$(FinbuckleMultiTenantVersion)" />
    <PackageReference Include="Finbuckle.MultiTenant.AspNetCore" Version="$(FinbuckleMultiTenantVersion)" />
    <PackageReference Include="Finbuckle.MultiTenant.EntityFrameworkCore" Version="$(FinbuckleMultiTenantVersion)" />
    <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.6.1" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="$(FluentValidationVersion)" />
  </ItemGroup>

  <!-- Portal projects -->
  <ItemGroup Condition="'$(IsPortalProject)' == 'true'">
    <PackageReference Include="Microsoft.AspNetCore.Components.QuickGrid" Version="$(MicrosoftAspNetCoreVersion)" />
    <PackageReference Include="Blazored.Toast" Version="$(BlazoredToastVersion)" />
    <PackageReference Include="Blazored.Modal" Version="$(BlazoredModalVersion)" />
    <PackageReference Include="MudBlazor" Version="$(MudBlazorVersion)" />
    <PackageReference Include="Finbuckle.MultiTenant.AspNetCore" Version="$(FinbuckleMultiTenantVersion)" />
  </ItemGroup>

  <!-- Core projects -->
  <ItemGroup Condition="'$(IsCoreProject)' == 'true'">
    <PackageReference Include="Finbuckle.MultiTenant" Version="$(FinbuckleMultiTenantVersion)" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="$(MicrosoftAspNetCoreVersion)" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="$(MicrosoftAspNetCoreVersion)" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="$(JwtVersion)" />
    <PackageReference Include="BCrypt.Net-Next" Version="$(BCryptVersion)" />
  </ItemGroup>

  <!-- Provider projects (minimal dependencies) -->
  <ItemGroup Condition="'$(IsProviderProject)' == 'true'">
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="$(MicrosoftExtensionsVersion)" />
    <PackageReference Include="System.Text.Json" Version="$(SystemTextJsonVersion)" />
  </ItemGroup>

  <!-- Custom targets -->
  <Target Name="PrintProjectInfo" BeforeTargets="Build" Condition="'$(Verbosity)' == 'diagnostic'">
    <Message Text="Building project: $(MSBuildProjectName)" Importance="high" />
    <Message Text="Target framework: $(TargetFramework)" Importance="high" />
    <Message Text="Configuration: $(Configuration)" Importance="high" />
    <Message Text="Output path: $(OutputPath)" Importance="high" />
  </Target>

  <!-- Clean generated files -->
  <Target Name="CleanGeneratedFiles" BeforeTargets="Clean">
    <ItemGroup>
      <GeneratedFiles Include="$(CompilerGeneratedFilesOutputPath)**/*" />
    </ItemGroup>
    <Delete Files="@(GeneratedFiles)" ContinueOnError="true" />
  </Target>

  <!-- Ensure output directories exist -->
  <Target Name="EnsureOutputDirectories" BeforeTargets="Build">
    <MakeDir Directories="$(OutputPath)" Condition="!Exists('$(OutputPath)')" />
    <MakeDir Directories="$(CompilerGeneratedFilesOutputPath)" Condition="!Exists('$(CompilerGeneratedFilesOutputPath)')" />
  </Target>

  <!-- Copy configuration files for web projects -->
  <Target Name="CopyConfigFiles" AfterTargets="Build" Condition="'$(IsWebProject)' == 'true' OR '$(IsApiProject)' == 'true' OR '$(IsPortalProject)' == 'true'">
    <ItemGroup>
      <ConfigFiles Include="appsettings*.json" />
    </ItemGroup>
    <Copy SourceFiles="@(ConfigFiles)" DestinationFolder="$(OutputPath)" SkipUnchangedFiles="true" />
  </Target>

</Project>
