using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace SmsGateway.Portal.Services;

/// <summary>
/// HTTP API service implementation
/// </summary>
public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public ApiService(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("SmsGatewayApi");
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync(endpoint, cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<T>(content, _jsonOptions);
            }
            
            return default;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"GET request failed: {ex.Message}");
            return default;
        }
    }

    public async Task<TResponse?> PostAsync<TRequest, TResponse>(string endpoint, TRequest data, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(data, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync(endpoint, content, cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<TResponse>(responseContent, _jsonOptions);
            }
            
            return default;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"POST request failed: {ex.Message}");
            return default;
        }
    }

    public async Task<TResponse?> PutAsync<TRequest, TResponse>(string endpoint, TRequest data, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(data, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync(endpoint, content, cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<TResponse>(responseContent, _jsonOptions);
            }
            
            return default;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"PUT request failed: {ex.Message}");
            return default;
        }
    }

    public async Task<bool> DeleteAsync(string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.DeleteAsync(endpoint, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"DELETE request failed: {ex.Message}");
            return false;
        }
    }

    public void SetAuthToken(string token)
    {
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
    }

    public void ClearAuthToken()
    {
        _httpClient.DefaultRequestHeaders.Authorization = null;
    }
}

/// <summary>
/// Authentication service implementation
/// </summary>
public class AuthService : IAuthService
{
    private readonly IApiService _apiService;
    private UserInfo? _currentUser;

    public AuthService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<bool> LoginAsync(string username, string password)
    {
        try
        {
            var loginRequest = new { Username = username, Password = password };
            var response = await _apiService.PostAsync<object, LoginResponse>("/api/auth/login", loginRequest);
            
            if (response?.Success == true && !string.IsNullOrEmpty(response.AccessToken))
            {
                _apiService.SetAuthToken(response.AccessToken);
                _currentUser = response.User;
                return true;
            }
            
            return false;
        }
        catch
        {
            return false;
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            await _apiService.PostAsync<object, object>("/api/auth/logout", new { });
        }
        catch
        {
            // Ignore logout errors
        }
        finally
        {
            _apiService.ClearAuthToken();
            _currentUser = null;
        }
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        if (_currentUser == null)
        {
            return false;
        }

        // Optionally validate token with server
        try
        {
            var user = await _apiService.GetAsync<UserInfo>("/api/auth/me");
            return user != null;
        }
        catch
        {
            return false;
        }
    }

    public async Task<UserInfo?> GetCurrentUserAsync()
    {
        if (_currentUser != null)
        {
            return _currentUser;
        }

        try
        {
            _currentUser = await _apiService.GetAsync<UserInfo>("/api/auth/me");
            return _currentUser;
        }
        catch
        {
            return null;
        }
    }

    public async Task<bool> RefreshTokenAsync()
    {
        try
        {
            var response = await _apiService.PostAsync<object, LoginResponse>("/api/auth/refresh", new { });
            
            if (response?.Success == true && !string.IsNullOrEmpty(response.AccessToken))
            {
                _apiService.SetAuthToken(response.AccessToken);
                return true;
            }
            
            return false;
        }
        catch
        {
            return false;
        }
    }

    private class LoginResponse
    {
        public bool Success { get; set; }
        public string? AccessToken { get; set; }
        public UserInfo? User { get; set; }
    }
}

/// <summary>
/// SMS service implementation
/// </summary>
public class SmsService : ISmsService
{
    private readonly IApiService _apiService;

    public SmsService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<SmsResponse?> SendSmsAsync(SmsRequest request)
    {
        return await _apiService.PostAsync<SmsRequest, SmsResponse>("/api/sms/send", request);
    }

    public async Task<List<SmsResponse>?> SendBulkSmsAsync(List<SmsRequest> requests)
    {
        return await _apiService.PostAsync<List<SmsRequest>, List<SmsResponse>>("/api/sms/send/batch", requests);
    }

    public async Task<List<SmsMessage>?> GetSmsHistoryAsync(int page = 1, int pageSize = 50)
    {
        return await _apiService.GetAsync<List<SmsMessage>>($"/api/sms/history?page={page}&pageSize={pageSize}");
    }

    public async Task<SmsStatistics?> GetSmsStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
    {
        var query = "";
        if (fromDate.HasValue)
            query += $"fromDate={fromDate.Value:yyyy-MM-dd}&";
        if (toDate.HasValue)
            query += $"toDate={toDate.Value:yyyy-MM-dd}&";
        
        return await _apiService.GetAsync<SmsStatistics>($"/api/sms/statistics?{query.TrimEnd('&')}");
    }

    public async Task<string?> GetMessageStatusAsync(string messageId, string provider)
    {
        var response = await _apiService.GetAsync<object>($"/api/sms/status/{messageId}?provider={provider}");
        return response?.ToString();
    }
}

/// <summary>
/// Template service implementation
/// </summary>
public class TemplateService : ITemplateService
{
    private readonly IApiService _apiService;

    public TemplateService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<SmsTemplate>?> GetTemplatesAsync()
    {
        return await _apiService.GetAsync<List<SmsTemplate>>("/api/templates");
    }

    public async Task<SmsTemplate?> GetTemplateAsync(Guid id)
    {
        return await _apiService.GetAsync<SmsTemplate>($"/api/templates/{id}");
    }

    public async Task<SmsTemplate?> CreateTemplateAsync(CreateTemplateRequest request)
    {
        return await _apiService.PostAsync<CreateTemplateRequest, SmsTemplate>("/api/templates", request);
    }

    public async Task<SmsTemplate?> UpdateTemplateAsync(Guid id, UpdateTemplateRequest request)
    {
        return await _apiService.PutAsync<UpdateTemplateRequest, SmsTemplate>($"/api/templates/{id}", request);
    }

    public async Task<bool> DeleteTemplateAsync(Guid id)
    {
        return await _apiService.DeleteAsync($"/api/templates/{id}");
    }

    public async Task<string?> TestTemplateAsync(string content, Dictionary<string, object> variables)
    {
        var request = new { Content = content, Variables = variables };
        var response = await _apiService.PostAsync<object, object>("/api/templates/test", request);
        return response?.ToString();
    }
}

/// <summary>
/// Provider service implementation
/// </summary>
public class ProviderService : IProviderService
{
    private readonly IApiService _apiService;

    public ProviderService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<List<ProviderInfo>?> GetProvidersAsync()
    {
        return await _apiService.GetAsync<List<ProviderInfo>>("/api/providers");
    }

    public async Task<List<ProviderHealthStatus>?> GetProviderHealthAsync()
    {
        return await _apiService.GetAsync<List<ProviderHealthStatus>>("/api/providers/health");
    }

    public async Task<List<ProviderStatistics>?> GetProviderStatisticsAsync()
    {
        return await _apiService.GetAsync<List<ProviderStatistics>>("/api/providers/statistics");
    }

    public async Task<List<PluginInfo>?> GetLoadedPluginsAsync()
    {
        return await _apiService.GetAsync<List<PluginInfo>>("/api/providers/plugins");
    }

    public async Task<bool> LoadPluginAsync(string pluginPath)
    {
        var request = new { PluginPath = pluginPath };
        var response = await _apiService.PostAsync<object, object>("/api/providers/plugins/load", request);
        return response != null;
    }

    public async Task<bool> UnloadPluginAsync(string pluginName)
    {
        return await _apiService.DeleteAsync($"/api/providers/plugins/{pluginName}");
    }
}
