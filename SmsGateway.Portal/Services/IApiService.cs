namespace SmsGateway.Portal.Services;

/// <summary>
/// Base API service interface for HTTP operations
/// </summary>
public interface IApiService
{
    /// <summary>
    /// Send GET request
    /// </summary>
    Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send POST request
    /// </summary>
    Task<TResponse?> PostAsync<TRequest, TResponse>(string endpoint, TRequest data, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send PUT request
    /// </summary>
    Task<TResponse?> PutAsync<TRequest, TResponse>(string endpoint, TRequest data, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send DELETE request
    /// </summary>
    Task<bool> DeleteAsync(string endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set authentication token
    /// </summary>
    void SetAuthToken(string token);

    /// <summary>
    /// Clear authentication token
    /// </summary>
    void ClearAuthToken();
}

/// <summary>
/// Authentication service interface
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// Login user
    /// </summary>
    Task<bool> LoginAsync(string username, string password);

    /// <summary>
    /// Logout user
    /// </summary>
    Task LogoutAsync();

    /// <summary>
    /// Check if user is authenticated
    /// </summary>
    Task<bool> IsAuthenticatedAsync();

    /// <summary>
    /// Get current user info
    /// </summary>
    Task<UserInfo?> GetCurrentUserAsync();

    /// <summary>
    /// Refresh authentication token
    /// </summary>
    Task<bool> RefreshTokenAsync();
}

/// <summary>
/// SMS service interface for portal
/// </summary>
public interface ISmsService
{
    /// <summary>
    /// Send single SMS
    /// </summary>
    Task<SmsResponse?> SendSmsAsync(SmsRequest request);

    /// <summary>
    /// Send bulk SMS
    /// </summary>
    Task<List<SmsResponse>?> SendBulkSmsAsync(List<SmsRequest> requests);

    /// <summary>
    /// Get SMS history
    /// </summary>
    Task<List<SmsMessage>?> GetSmsHistoryAsync(int page = 1, int pageSize = 50);

    /// <summary>
    /// Get SMS statistics
    /// </summary>
    Task<SmsStatistics?> GetSmsStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);

    /// <summary>
    /// Get message status
    /// </summary>
    Task<string?> GetMessageStatusAsync(string messageId, string provider);
}

/// <summary>
/// Template service interface for portal
/// </summary>
public interface ITemplateService
{
    /// <summary>
    /// Get all templates
    /// </summary>
    Task<List<SmsTemplate>?> GetTemplatesAsync();

    /// <summary>
    /// Get template by ID
    /// </summary>
    Task<SmsTemplate?> GetTemplateAsync(Guid id);

    /// <summary>
    /// Create template
    /// </summary>
    Task<SmsTemplate?> CreateTemplateAsync(CreateTemplateRequest request);

    /// <summary>
    /// Update template
    /// </summary>
    Task<SmsTemplate?> UpdateTemplateAsync(Guid id, UpdateTemplateRequest request);

    /// <summary>
    /// Delete template
    /// </summary>
    Task<bool> DeleteTemplateAsync(Guid id);

    /// <summary>
    /// Test template
    /// </summary>
    Task<string?> TestTemplateAsync(string content, Dictionary<string, object> variables);
}

/// <summary>
/// Provider service interface for portal
/// </summary>
public interface IProviderService
{
    /// <summary>
    /// Get all providers
    /// </summary>
    Task<List<ProviderInfo>?> GetProvidersAsync();

    /// <summary>
    /// Get provider health status
    /// </summary>
    Task<List<ProviderHealthStatus>?> GetProviderHealthAsync();

    /// <summary>
    /// Get provider statistics
    /// </summary>
    Task<List<ProviderStatistics>?> GetProviderStatisticsAsync();

    /// <summary>
    /// Get loaded plugins
    /// </summary>
    Task<List<PluginInfo>?> GetLoadedPluginsAsync();

    /// <summary>
    /// Load plugin
    /// </summary>
    Task<bool> LoadPluginAsync(string pluginPath);

    /// <summary>
    /// Unload plugin
    /// </summary>
    Task<bool> UnloadPluginAsync(string pluginName);
}

// DTOs for the portal
public class UserInfo
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
}

public class SmsRequest
{
    public string To { get; set; } = string.Empty;
    public string? From { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Provider { get; set; }
    public Guid? TemplateId { get; set; }
    public Dictionary<string, object>? TemplateVariables { get; set; }
    public DateTime? ScheduledAt { get; set; }
}

public class SmsResponse
{
    public bool Success { get; set; }
    public string? MessageId { get; set; }
    public string Provider { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; }
}

public class SmsMessage
{
    public Guid Id { get; set; }
    public string To { get; set; } = string.Empty;
    public string? From { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? SentAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public class SmsStatistics
{
    public int TotalMessages { get; set; }
    public int SentMessages { get; set; }
    public int DeliveredMessages { get; set; }
    public int FailedMessages { get; set; }
    public int PendingMessages { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal TotalCost { get; set; }
    public string Currency { get; set; } = "USD";
}

public class SmsTemplate
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Category { get; set; }
    public bool IsActive { get; set; }
    public int UsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CreateTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Category { get; set; }
    public bool IsActive { get; set; } = true;
}

public class UpdateTemplateRequest : CreateTemplateRequest
{
}

public class ProviderInfo
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public bool IsAvailable { get; set; }
    public int Priority { get; set; }
}

public class ProviderHealthStatus
{
    public string ProviderName { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public string? Message { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public DateTime CheckedAt { get; set; }
}

public class ProviderStatistics
{
    public string ProviderName { get; set; } = string.Empty;
    public int TotalMessagesSent { get; set; }
    public int SuccessfulMessages { get; set; }
    public int FailedMessages { get; set; }
    public decimal SuccessRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public DateTime LastUsed { get; set; }
    public bool IsHealthy { get; set; }
}

public class PluginInfo
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsLoaded { get; set; }
    public DateTime LoadedAt { get; set; }
    public List<string> ProviderNames { get; set; } = new();
}
