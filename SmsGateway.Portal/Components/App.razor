<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <title>SMS Gateway Portal</title>
    
    <!-- MudBlazor CSS -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    
    <!-- Blazored Toast CSS -->
    <link href="_content/Blazored.Toast/blazored-toast.min.css" rel="stylesheet" />
    
    <!-- Blazored Modal CSS -->
    <link href="_content/Blazored.Modal/blazored-modal.min.css" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/app.css" />
    <link rel="stylesheet" href="SmsGateway.Portal.styles.css" />
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <!-- MudBlazor JS -->
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    
    <!-- Blazor JS -->
    <script src="_framework/blazor.server.js"></script>
</body>

</html>

@code {
    
}
