@page "/sms/send"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using MudBlazor
@using SmsGateway.Portal.Services
@inject ISmsService SmsService
@inject ITemplateService TemplateService
@inject IProviderService ProviderService
@inject ISnackbar Snackbar
@attribute [Authorize]

<PageTitle>Send SMS - SMS Gateway Portal</PageTitle>

<MudText Typo="Typo.h3" GutterBottom="true">Send SMS</MudText>
<MudText Class="mb-8">Send individual SMS messages using templates or custom content.</MudText>

<MudGrid>
    <MudItem xs="12" md="8">
        <MudCard Elevation="2">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Message Details</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <EditForm Model="@_smsRequest" OnValidSubmit="SendSms">
                    <DataAnnotationsValidator />
                    
                    <MudStack Spacing="4">
                        <MudTextField @bind-Value="_smsRequest.To"
                                    Label="Recipient Phone Number"
                                    Placeholder="+**********"
                                    Variant="Variant.Outlined"
                                    Required="true"
                                    For="@(() => _smsRequest.To)"
                                    Adornment="Adornment.Start"
                                    AdornmentIcon="Icons.Material.Filled.Phone"
                                    HelperText="Enter phone number in international format (e.g., +**********)" />

                        <MudTextField @bind-Value="_smsRequest.From"
                                    Label="Sender ID (Optional)"
                                    Placeholder="YourBrand"
                                    Variant="Variant.Outlined"
                                    For="@(() => _smsRequest.From)"
                                    Adornment="Adornment.Start"
                                    AdornmentIcon="Icons.Material.Filled.Badge"
                                    HelperText="Leave empty to use default sender ID" />

                        <MudSelect @bind-Value="_selectedProvider"
                                 Label="SMS Provider"
                                 Variant="Variant.Outlined"
                                 AnchorOrigin="Origin.BottomCenter">
                            <MudSelectItem Value="@("")">Auto-select best provider</MudSelectItem>
                            @if (_providers != null)
                            {
                                @foreach (var provider in _providers.Where(p => p.IsEnabled && p.IsAvailable))
                                {
                                    <MudSelectItem Value="@provider.Name">
                                        @provider.DisplayName (@provider.Name)
                                    </MudSelectItem>
                                }
                            }
                        </MudSelect>

                        <MudSelect @bind-Value="_selectedTemplateId"
                                 Label="Template (Optional)"
                                 Variant="Variant.Outlined"
                                 AnchorOrigin="Origin.BottomCenter"
                                 T="Guid?"
                                 ToStringFunc="@(id => GetTemplateName(id))">
                            <MudSelectItem Value="@((Guid?)null)">No template - custom message</MudSelectItem>
                            @if (_templates != null)
                            {
                                @foreach (var template in _templates.Where(t => t.IsActive))
                                {
                                    <MudSelectItem Value="@((Guid?)template.Id)">
                                        @template.DisplayName (@template.Name)
                                    </MudSelectItem>
                                }
                            }
                        </MudSelect>

                        @if (_selectedTemplateId.HasValue)
                        {
                            <MudCard Outlined="true" Class="pa-4">
                                <MudText Typo="Typo.h6" Class="mb-3">Template Variables</MudText>
                                @if (_templateVariables.Any())
                                {
                                    @foreach (var variable in _templateVariables)
                                    {
                                        <MudTextField @bind-Value="variable.Value"
                                                    Label="@variable.Key"
                                                    Variant="Variant.Outlined"
                                                    Class="mb-2"
                                                    HelperText="@($"Variable: {{{variable.Key}}}")" />
                                    }
                                }
                                else
                                {
                                    <MudText Color="Color.TextSecondary">No variables required for this template</MudText>
                                }
                            </MudCard>
                        }

                        <MudTextField @bind-Value="_smsRequest.Message"
                                    Label="Message Content"
                                    Variant="Variant.Outlined"
                                    Lines="4"
                                    Required="@(!_selectedTemplateId.HasValue)"
                                    For="@(() => _smsRequest.Message)"
                                    Counter="160"
                                    MaxLength="1600"
                                    HelperText="@GetMessageHelperText()" />

                        <MudDatePicker @bind-Date="_scheduledDate"
                                     Label="Scheduled Send Date (Optional)"
                                     Variant="Variant.Outlined"
                                     MinDate="DateTime.Today"
                                     Editable="true" />

                        @if (_scheduledDate.HasValue)
                        {
                            <MudTimePicker @bind-Time="_scheduledTime"
                                         Label="Scheduled Send Time"
                                         Variant="Variant.Outlined"
                                         Editable="true" />
                        }

                        <MudButton ButtonType="ButtonType.Submit"
                                 Variant="Variant.Filled"
                                 Color="Color.Primary"
                                 Size="Size.Large"
                                 StartIcon="Icons.Material.Filled.Send"
                                 Disabled="@_isSending"
                                 FullWidth="true">
                            @if (_isSending)
                            {
                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                                <MudText Class="ms-2">Sending...</MudText>
                            }
                            else
                            {
                                <MudText>Send SMS</MudText>
                            }
                        </MudButton>
                    </MudStack>
                </EditForm>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" md="4">
        <MudCard Elevation="2">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Message Preview</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudPaper Outlined="true" Class="pa-4 mb-4" Style="min-height: 120px;">
                    <MudText Typo="Typo.body2" Color="Color.TextSecondary" Class="mb-2">Preview:</MudText>
                    <MudText Typo="Typo.body1">
                        @GetPreviewMessage()
                    </MudText>
                </MudPaper>

                <MudStack Spacing="2">
                    <div class="d-flex justify-space-between">
                        <MudText Typo="Typo.body2">Characters:</MudText>
                        <MudText Typo="Typo.body2">@GetPreviewMessage().Length / 1600</MudText>
                    </div>
                    <div class="d-flex justify-space-between">
                        <MudText Typo="Typo.body2">Estimated Segments:</MudText>
                        <MudText Typo="Typo.body2">@CalculateSegments(GetPreviewMessage())</MudText>
                    </div>
                    <div class="d-flex justify-space-between">
                        <MudText Typo="Typo.body2">Provider:</MudText>
                        <MudText Typo="Typo.body2">@(string.IsNullOrEmpty(_selectedProvider) ? "Auto" : _selectedProvider)</MudText>
                    </div>
                    @if (_scheduledDate.HasValue)
                    {
                        <div class="d-flex justify-space-between">
                            <MudText Typo="Typo.body2">Scheduled:</MudText>
                            <MudText Typo="Typo.body2">@GetScheduledDateTime()?.ToString("MMM dd, HH:mm")</MudText>
                        </div>
                    }
                </MudStack>
            </MudCardContent>
        </MudCard>

        @if (_lastResponse != null)
        {
            <MudCard Elevation="2" Class="mt-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Last Result</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudAlert Severity="@(_lastResponse.Success ? Severity.Success : Severity.Error)">
                        @if (_lastResponse.Success)
                        {
                            <MudText>Message sent successfully!</MudText>
                            <MudText Typo="Typo.body2">Message ID: @_lastResponse.MessageId</MudText>
                            <MudText Typo="Typo.body2">Provider: @_lastResponse.Provider</MudText>
                        }
                        else
                        {
                            <MudText>Failed to send message</MudText>
                            <MudText Typo="Typo.body2">Error: @_lastResponse.ErrorMessage</MudText>
                        }
                    </MudAlert>
                </MudCardContent>
            </MudCard>
        }
    </MudItem>
</MudGrid>

@code {
    private readonly SmsRequest _smsRequest = new();
    private List<ProviderInfo>? _providers;
    private List<SmsTemplate>? _templates;
    private string _selectedProvider = string.Empty;
    private Guid? _selectedTemplateId;
    private DateTime? _scheduledDate;
    private TimeSpan? _scheduledTime;
    private bool _isSending = false;
    private SmsResponse? _lastResponse;
    private Dictionary<string, string> _templateVariables = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadProvidersAsync();
        await LoadTemplatesAsync();
    }

    private async Task LoadProvidersAsync()
    {
        try
        {
            _providers = await ProviderService.GetProvidersAsync() ?? new List<ProviderInfo>();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Failed to load providers: {ex.Message}", Severity.Error);
            _providers = new List<ProviderInfo>();
        }
    }

    private async Task LoadTemplatesAsync()
    {
        try
        {
            _templates = await TemplateService.GetTemplatesAsync() ?? new List<SmsTemplate>();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Failed to load templates: {ex.Message}", Severity.Error);
            _templates = new List<SmsTemplate>();
        }
    }

    private async Task SendSms()
    {
        _isSending = true;
        _lastResponse = null;

        try
        {
            // Set provider
            _smsRequest.Provider = string.IsNullOrEmpty(_selectedProvider) ? null : _selectedProvider;
            
            // Set template
            _smsRequest.TemplateId = _selectedTemplateId;
            
            // Set template variables
            if (_selectedTemplateId.HasValue && _templateVariables.Any())
            {
                _smsRequest.TemplateVariables = _templateVariables.ToDictionary(kv => kv.Key, kv => (object)kv.Value);
            }
            
            // Set scheduled time
            _smsRequest.ScheduledAt = GetScheduledDateTime();

            _lastResponse = await SmsService.SendSmsAsync(_smsRequest);

            if (_lastResponse?.Success == true)
            {
                Snackbar.Add("SMS sent successfully!", Severity.Success);
                // Reset form
                _smsRequest.To = string.Empty;
                _smsRequest.Message = string.Empty;
                _selectedTemplateId = null;
                _scheduledDate = null;
                _scheduledTime = null;
                _templateVariables.Clear();
            }
            else
            {
                Snackbar.Add($"Failed to send SMS: {_lastResponse?.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error sending SMS: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isSending = false;
        }
    }

    private string GetTemplateName(Guid? templateId)
    {
        if (!templateId.HasValue) return "No template - custom message";
        var template = _templates?.FirstOrDefault(t => t.Id == templateId.Value);
        return template != null ? $"{template.DisplayName} ({template.Name})" : "Unknown template";
    }

    private string GetPreviewMessage()
    {
        if (_selectedTemplateId.HasValue)
        {
            var template = _templates?.FirstOrDefault(t => t.Id == _selectedTemplateId.Value);
            if (template != null)
            {
                var content = template.Content;
                foreach (var variable in _templateVariables)
                {
                    content = content.Replace($"{{{variable.Key}}}", variable.Value);
                }
                return content;
            }
        }
        return _smsRequest.Message;
    }

    private string GetMessageHelperText()
    {
        var segments = CalculateSegments(GetPreviewMessage());
        return $"Estimated segments: {segments} | Characters: {GetPreviewMessage().Length}/1600";
    }

    private int CalculateSegments(string message)
    {
        if (string.IsNullOrEmpty(message)) return 0;
        var containsUnicode = message.Any(c => c > 127);
        var maxLength = containsUnicode ? 70 : 160;
        return (int)Math.Ceiling((double)message.Length / maxLength);
    }

    private DateTime? GetScheduledDateTime()
    {
        if (_scheduledDate.HasValue)
        {
            var date = _scheduledDate.Value;
            if (_scheduledTime.HasValue)
            {
                date = date.Add(_scheduledTime.Value);
            }
            return date;
        }
        return null;
    }
}
