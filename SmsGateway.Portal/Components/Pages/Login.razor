@page "/login"
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Components.Authorization
@using MudBlazor
@using SmsGateway.Portal.Services
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<PageTitle>Login - SMS Gateway Portal</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="mt-16">
    <MudPaper Elevation="4" Class="pa-8">
        <MudText Typo="Typo.h4" Align="Align.Center" GutterBottom="true">
            SMS Gateway Portal
        </MudText>
        <MudText Typo="Typo.body1" Align="Align.Center" Class="mb-6" Color="Color.TextSecondary">
            Sign in to your account
        </MudText>

        <EditForm Model="@_loginModel" OnValidSubmit="HandleLogin">
            <DataAnnotationsValidator />
            
            <MudStack Spacing="4">
                <MudTextField @bind-Value="_loginModel.Username"
                            Label="Username or Email"
                            Variant="Variant.Outlined"
                            Required="true"
                            For="@(() => _loginModel.Username)"
                            Disabled="@_isLoading"
                            Adornment="Adornment.Start"
                            AdornmentIcon="Icons.Material.Filled.Person" />

                <MudTextField @bind-Value="_loginModel.Password"
                            Label="Password"
                            Variant="Variant.Outlined"
                            InputType="@(_showPassword ? InputType.Text : InputType.Password)"
                            Required="true"
                            For="@(() => _loginModel.Password)"
                            Disabled="@_isLoading"
                            Adornment="Adornment.End"
                            AdornmentIcon="@(_showPassword ? Icons.Material.Filled.VisibilityOff : Icons.Material.Filled.Visibility)"
                            OnAdornmentClick="TogglePasswordVisibility" />

                <MudCheckBox @bind-Value="_loginModel.RememberMe"
                           Label="Remember me"
                           Color="Color.Primary"
                           Disabled="@_isLoading" />

                <MudButton ButtonType="ButtonType.Submit"
                         Variant="Variant.Filled"
                         Color="Color.Primary"
                         Size="Size.Large"
                         FullWidth="true"
                         Disabled="@_isLoading">
                    @if (_isLoading)
                    {
                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                        <MudText Class="ms-2">Signing in...</MudText>
                    }
                    else
                    {
                        <MudText>Sign In</MudText>
                    }
                </MudButton>
            </MudStack>
        </EditForm>

        <MudDivider Class="my-6" />

        <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
            <MudLink Href="/forgot-password" Color="Color.Primary">
                Forgot password?
            </MudLink>
            <MudLink Href="/register" Color="Color.Primary">
                Create account
            </MudLink>
        </MudStack>

        @if (!string.IsNullOrEmpty(_errorMessage))
        {
            <MudAlert Severity="Severity.Error" Class="mt-4">
                @_errorMessage
            </MudAlert>
        }
    </MudPaper>

    <MudText Typo="Typo.caption" Align="Align.Center" Class="mt-4" Color="Color.TextSecondary">
        SMS Gateway Portal v1.0 - Secure messaging platform
    </MudText>
</MudContainer>

@code {
    private readonly LoginModel _loginModel = new();
    private bool _isLoading = false;
    private bool _showPassword = false;
    private string _errorMessage = string.Empty;

    [Parameter]
    [SupplyParameterFromQuery]
    public string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo(ReturnUrl ?? "/");
        }
    }

    private async Task HandleLogin()
    {
        _isLoading = true;
        _errorMessage = string.Empty;

        try
        {
            var success = await AuthStateProvider.LoginAsync(_loginModel.Username, _loginModel.Password);
            
            if (success)
            {
                Snackbar.Add("Login successful!", Severity.Success);
                Navigation.NavigateTo(ReturnUrl ?? "/");
            }
            else
            {
                _errorMessage = "Invalid username or password. Please try again.";
                Snackbar.Add("Login failed", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _errorMessage = "An error occurred during login. Please try again.";
            Snackbar.Add($"Login error: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private void TogglePasswordVisibility()
    {
        _showPassword = !_showPassword;
    }

    private class LoginModel
    {
        [Required]
        public string Username { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }
}
