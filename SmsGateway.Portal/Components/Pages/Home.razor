@page "/"
@using Microsoft.AspNetCore.Components.Authorization
@using MudBlazor
@using SmsGateway.Portal.Services
@inject IApiService ApiService
@inject ISmsService SmsService

<PageTitle>Dashboard - SMS Gateway Portal</PageTitle>

<MudText Typo="Typo.h3" GutterBottom="true">Dashboard</MudText>
<MudText Class="mb-8">Welcome to the SMS Gateway Portal. Monitor your SMS operations and manage your messaging infrastructure.</MudText>

<AuthorizeView>
    <Authorized>
        <MudGrid>
            <!-- Statistics Cards -->
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent>
                        <div class="d-flex align-center">
                            <MudIcon Icon="Icons.Material.Filled.Send" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">@_totalMessages</MudText>
                                <MudText Typo="Typo.body2" Color="Color.TextSecondary">Total Messages</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent>
                        <div class="d-flex align-center">
                            <MudIcon Icon="Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">@_deliveredMessages</MudText>
                                <MudText Typo="Typo.body2" Color="Color.TextSecondary">Delivered</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent>
                        <div class="d-flex align-center">
                            <MudIcon Icon="Icons.Material.Filled.Error" Color="Color.Error" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">@_failedMessages</MudText>
                                <MudText Typo="Typo.body2" Color="Color.TextSecondary">Failed</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudCard Elevation="2" Class="pa-4">
                    <MudCardContent>
                        <div class="d-flex align-center">
                            <MudIcon Icon="Icons.Material.Filled.Schedule" Color="Color.Warning" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h4">@_queuedMessages</MudText>
                                <MudText Typo="Typo.body2" Color="Color.TextSecondary">In Queue</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <!-- Quick Actions -->
            <MudItem xs="12" md="6">
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Quick Actions</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Spacing="3">
                            <MudButton Variant="Variant.Filled" 
                                     Color="Color.Primary" 
                                     StartIcon="Icons.Material.Filled.Send"
                                     FullWidth="true"
                                     Href="/sms/send">
                                Send SMS
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Secondary" 
                                     StartIcon="Icons.Material.Filled.GroupWork"
                                     FullWidth="true"
                                     Href="/sms/bulk">
                                Bulk SMS
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Info" 
                                     StartIcon="Icons.Material.Filled.Add"
                                     FullWidth="true"
                                     Href="/templates/create">
                                Create Template
                            </MudButton>
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <!-- Provider Status -->
            <MudItem xs="12" md="6">
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Provider Status</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_providerStatuses != null && _providerStatuses.Any())
                        {
                            <MudStack Spacing="2">
                                @foreach (var provider in _providerStatuses)
                                {
                                    <div class="d-flex align-center justify-space-between">
                                        <MudText>@provider.Name</MudText>
                                        <MudChip Color="@(provider.IsHealthy ? Color.Success : Color.Error)" 
                                               Size="Size.Small">
                                            @(provider.IsHealthy ? "Healthy" : "Unhealthy")
                                        </MudChip>
                                    </div>
                                }
                            </MudStack>
                        }
                        else
                        {
                            <MudText Color="Color.TextSecondary">No providers configured</MudText>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>
            
            <!-- Recent Messages -->
            <MudItem xs="12">
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Recent Messages</MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudButton Color="Color.Primary" 
                                     Variant="Variant.Text" 
                                     Href="/sms/history">
                                View All
                            </MudButton>
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_recentMessages != null && _recentMessages.Any())
                        {
                            <MudTable Items="@_recentMessages" Dense="true" Hover="true">
                                <HeaderContent>
                                    <MudTh>To</MudTh>
                                    <MudTh>Message</MudTh>
                                    <MudTh>Status</MudTh>
                                    <MudTh>Provider</MudTh>
                                    <MudTh>Sent At</MudTh>
                                </HeaderContent>
                                <RowTemplate>
                                    <MudTd DataLabel="To">@context.To</MudTd>
                                    <MudTd DataLabel="Message">
                                        @(context.Message.Length > 50 ? context.Message.Substring(0, 50) + "..." : context.Message)
                                    </MudTd>
                                    <MudTd DataLabel="Status">
                                        <MudChip Color="@GetStatusColor(context.Status)" Size="Size.Small">
                                            @context.Status
                                        </MudChip>
                                    </MudTd>
                                    <MudTd DataLabel="Provider">@context.Provider</MudTd>
                                    <MudTd DataLabel="Sent At">@context.SentAt?.ToString("MMM dd, HH:mm")</MudTd>
                                </RowTemplate>
                            </MudTable>
                        }
                        else
                        {
                            <MudText Color="Color.TextSecondary">No recent messages</MudText>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Info" Class="mb-4">
            Please <MudLink Href="/login">login</MudLink> to access the SMS Gateway Portal.
        </MudAlert>
        
        <MudCard Elevation="2" Class="pa-6">
            <MudCardContent>
                <MudText Typo="Typo.h4" GutterBottom="true">SMS Gateway Portal</MudText>
                <MudText Typo="Typo.body1" Class="mb-4">
                    A comprehensive SMS messaging platform with multi-tenancy, templating, and plugin support.
                </MudText>
                
                <MudText Typo="Typo.h6" GutterBottom="true">Features:</MudText>
                <MudList>
                    <MudListItem Icon="Icons.Material.Filled.Send" Text="Send SMS messages via multiple providers" />
                    <MudListItem Icon="Icons.Material.Filled.Description" Text="Template management with variable substitution" />
                    <MudListItem Icon="Icons.Material.Filled.Queue" Text="Background processing with Hangfire" />
                    <MudListItem Icon="Icons.Material.Filled.Business" Text="Multi-tenant architecture" />
                    <MudListItem Icon="Icons.Material.Filled.Extension" Text="Plugin-based provider system" />
                    <MudListItem Icon="Icons.Material.Filled.Analytics" Text="Comprehensive reporting and analytics" />
                </MudList>
                
                <MudButton Variant="Variant.Filled" 
                         Color="Color.Primary" 
                         Class="mt-4"
                         Href="/login">
                    Get Started
                </MudButton>
            </MudCardContent>
        </MudCard>
    </NotAuthorized>
</AuthorizeView>

@code {
    private int _totalMessages = 0;
    private int _deliveredMessages = 0;
    private int _failedMessages = 0;
    private int _queuedMessages = 0;
    
    private List<ProviderStatus>? _providerStatuses;
    private List<RecentMessage>? _recentMessages;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            // Load statistics
            // _totalMessages = await SmsService.GetTotalMessagesAsync();
            // _deliveredMessages = await SmsService.GetDeliveredMessagesAsync();
            // _failedMessages = await SmsService.GetFailedMessagesAsync();
            // _queuedMessages = await SmsService.GetQueuedMessagesAsync();
            
            // Mock data for now
            _totalMessages = 1234;
            _deliveredMessages = 1156;
            _failedMessages = 23;
            _queuedMessages = 55;
            
            // Load provider statuses
            _providerStatuses = new List<ProviderStatus>
            {
                new() { Name = "Twilio", IsHealthy = true },
                new() { Name = "BulkSMS", IsHealthy = true },
                new() { Name = "Mock", IsHealthy = true }
            };
            
            // Load recent messages
            _recentMessages = new List<RecentMessage>
            {
                new() { To = "+**********", Message = "Welcome to our service!", Status = "Delivered", Provider = "Twilio", SentAt = DateTime.Now.AddMinutes(-5) },
                new() { To = "+**********", Message = "Your verification code is 123456", Status = "Sent", Provider = "BulkSMS", SentAt = DateTime.Now.AddMinutes(-10) },
                new() { To = "+**********", Message = "Thank you for your purchase", Status = "Delivered", Provider = "Twilio", SentAt = DateTime.Now.AddMinutes(-15) }
            };
        }
        catch (Exception ex)
        {
            // Handle error
            Console.WriteLine($"Error loading dashboard data: {ex.Message}");
        }
    }

    private Color GetStatusColor(string status)
    {
        return status.ToLower() switch
        {
            "delivered" => Color.Success,
            "sent" => Color.Info,
            "failed" => Color.Error,
            "pending" => Color.Warning,
            _ => Color.Default
        };
    }

    private class ProviderStatus
    {
        public string Name { get; set; } = string.Empty;
        public bool IsHealthy { get; set; }
    }

    private class RecentMessage
    {
        public string To { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Provider { get; set; } = string.Empty;
        public DateTime? SentAt { get; set; }
    }
}
