@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Routing
@using MudBlazor

<MudNavMenu>
    <MudText Typo="Typo.h6" Class="px-4 py-2">Navigation</MudText>
    <MudDivider Class="mb-2" />
    
    <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="Icons.Material.Filled.Dashboard">
        Dashboard
    </MudNavLink>
    
    <AuthorizeView>
        <Authorized>
            <MudNavGroup Text="SMS" Icon="Icons.Material.Filled.Sms" Expanded="true">
                <MudNavLink Href="/sms/send" Icon="Icons.Material.Filled.Send">
                    Send SMS
                </MudNavLink>
                <MudNavLink Href="/sms/history" Icon="Icons.Material.Filled.History">
                    Message History
                </MudNavLink>
                <MudNavLink Href="/sms/bulk" Icon="Icons.Material.Filled.GroupWork">
                    Bulk SMS
                </MudNavLink>
                <MudNavLink Href="/sms/scheduled" Icon="Icons.Material.Filled.Schedule">
                    Scheduled Messages
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="Templates" Icon="Icons.Material.Filled.Description" Expanded="false">
                <MudNavLink Href="/templates" Icon="Icons.Material.Filled.List">
                    All Templates
                </MudNavLink>
                <MudNavLink Href="/templates/create" Icon="Icons.Material.Filled.Add">
                    Create Template
                </MudNavLink>
                <MudNavLink Href="/templates/test" Icon="Icons.Material.Filled.PlayArrow">
                    Test Template
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="Providers" Icon="Icons.Material.Filled.CloudQueue" Expanded="false">
                <MudNavLink Href="/providers" Icon="Icons.Material.Filled.List">
                    All Providers
                </MudNavLink>
                <MudNavLink Href="/providers/health" Icon="Icons.Material.Filled.HealthAndSafety">
                    Health Status
                </MudNavLink>
                <MudNavLink Href="/providers/statistics" Icon="Icons.Material.Filled.Analytics">
                    Statistics
                </MudNavLink>
                <MudNavLink Href="/providers/plugins" Icon="Icons.Material.Filled.Extension">
                    Plugin Management
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="Queue" Icon="Icons.Material.Filled.Queue" Expanded="false">
                <MudNavLink Href="/queue/status" Icon="Icons.Material.Filled.ViewList">
                    Queue Status
                </MudNavLink>
                <MudNavLink Href="/queue/failed" Icon="Icons.Material.Filled.Error">
                    Failed Messages
                </MudNavLink>
                <MudNavLink Href="/queue/statistics" Icon="Icons.Material.Filled.BarChart">
                    Queue Statistics
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="Reports" Icon="Icons.Material.Filled.Assessment" Expanded="false">
                <MudNavLink Href="/reports/overview" Icon="Icons.Material.Filled.Dashboard">
                    Overview
                </MudNavLink>
                <MudNavLink Href="/reports/usage" Icon="Icons.Material.Filled.TrendingUp">
                    Usage Reports
                </MudNavLink>
                <MudNavLink Href="/reports/costs" Icon="Icons.Material.Filled.AttachMoney">
                    Cost Analysis
                </MudNavLink>
                <MudNavLink Href="/reports/delivery" Icon="Icons.Material.Filled.LocalShipping">
                    Delivery Reports
                </MudNavLink>
            </MudNavGroup>
            
            <AuthorizeView Policy="AdminOnly">
                <MudNavGroup Text="Administration" Icon="Icons.Material.Filled.AdminPanelSettings" Expanded="false">
                    <MudNavLink Href="/admin/users" Icon="Icons.Material.Filled.People">
                        User Management
                    </MudNavLink>
                    <MudNavLink Href="/admin/roles" Icon="Icons.Material.Filled.Security">
                        Role Management
                    </MudNavLink>
                    <MudNavLink Href="/admin/tenants" Icon="Icons.Material.Filled.Business">
                        Tenant Management
                    </MudNavLink>
                    <MudNavLink Href="/admin/api-keys" Icon="Icons.Material.Filled.VpnKey">
                        API Keys
                    </MudNavLink>
                    <MudNavLink Href="/admin/system" Icon="Icons.Material.Filled.Settings">
                        System Settings
                    </MudNavLink>
                    <MudNavLink Href="/admin/logs" Icon="Icons.Material.Filled.BugReport">
                        System Logs
                    </MudNavLink>
                </MudNavGroup>
            </AuthorizeView>
            
            <MudDivider Class="my-2" />
            
            <MudNavLink Href="/profile" Icon="Icons.Material.Filled.Person">
                My Profile
            </MudNavLink>
            <MudNavLink Href="/settings" Icon="Icons.Material.Filled.Settings">
                Settings
            </MudNavLink>
            <MudNavLink Href="/help" Icon="Icons.Material.Filled.Help">
                Help & Support
            </MudNavLink>
        </Authorized>
        <NotAuthorized>
            <MudNavLink Href="/login" Icon="Icons.Material.Filled.Login">
                Login
            </MudNavLink>
            <MudNavLink Href="/register" Icon="Icons.Material.Filled.PersonAdd">
                Register
            </MudNavLink>
        </NotAuthorized>
    </AuthorizeView>
</MudNavMenu>

<div class="mt-auto pa-4">
    <MudText Typo="Typo.caption" Class="mud-text-secondary">
        SMS Gateway Portal v1.0
    </MudText>
</div>
