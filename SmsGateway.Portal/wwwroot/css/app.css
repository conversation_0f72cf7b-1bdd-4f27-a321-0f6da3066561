/* SMS Gateway Portal Custom Styles */

html, body {
    font-family: '<PERSON>o', sans-serif;
}

#app {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

.loading-progress circle {
    fill: none;
    stroke: #e0e0e0;
    stroke-width: 0.6rem;
    transform-origin: 50% 50%;
    transform: rotate(-90deg);
}

.loading-progress circle:last-child {
    stroke: #1976d2;
    stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
    transition: stroke-dasharray 0.05s ease-in-out;
}

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

.loading-progress-text:after {
    content: var(--blazor-load-percentage-text, "Loading");
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

/* Custom toast styles */
.success-toast {
    background-color: #4caf50 !important;
    color: white !important;
}

.error-toast {
    background-color: #f44336 !important;
    color: white !important;
}

/* Dashboard cards */
.dashboard-card {
    transition: transform 0.2s ease-in-out;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

/* Message preview */
.message-preview {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 16px;
    font-family: monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Status chips */
.status-chip-delivered {
    background-color: #4caf50 !important;
    color: white !important;
}

.status-chip-sent {
    background-color: #2196f3 !important;
    color: white !important;
}

.status-chip-failed {
    background-color: #f44336 !important;
    color: white !important;
}

.status-chip-pending {
    background-color: #ff9800 !important;
    color: white !important;
}

.status-chip-queued {
    background-color: #9c27b0 !important;
    color: white !important;
}

/* Provider status indicators */
.provider-healthy {
    color: #4caf50;
}

.provider-unhealthy {
    color: #f44336;
}

/* Template editor */
.template-editor {
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

/* Statistics cards */
.stat-card {
    text-align: center;
    padding: 24px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #666;
    margin-top: 8px;
}

/* Navigation improvements */
.mud-nav-link-text {
    font-weight: 500;
}

.mud-nav-group .mud-nav-link {
    padding-left: 32px;
}

/* Form improvements */
.form-section {
    margin-bottom: 32px;
}

.form-section-title {
    margin-bottom: 16px;
    font-weight: 600;
    color: #333;
}

/* Table improvements */
.data-table .mud-table-cell {
    padding: 12px 16px;
}

.data-table .mud-table-head .mud-table-cell {
    font-weight: 600;
    background-color: #f5f5f5;
}

/* Button improvements */
.action-button {
    margin: 4px;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .mud-container {
        padding: 8px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .mud-card {
        margin-bottom: 16px;
    }
}

/* Dark theme adjustments */
.mud-theme-dark .message-preview {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #e0e0e0;
}

.mud-theme-dark .data-table .mud-table-head .mud-table-cell {
    background-color: #2d2d2d;
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.mud-theme-dark .loading-overlay {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
