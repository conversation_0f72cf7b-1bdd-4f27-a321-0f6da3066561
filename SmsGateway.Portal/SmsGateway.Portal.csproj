<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyTitle>SMS Gateway Portal</AssemblyTitle>
        <AssemblyDescription>Blazor Server UI Portal for SMS Gateway management</AssemblyDescription>
        <AssemblyVersion>*******</AssemblyVersion>
        <FileVersion>*******</FileVersion>
        <Authors>SMS Gateway Team</Authors>
        <Company>SMS Gateway</Company>
        <Product>SMS Gateway Portal</Product>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Components.QuickGrid" Version="9.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
        <PackageReference Include="Blazored.Toast" Version="4.2.1" />
        <PackageReference Include="Blazored.Modal" Version="7.3.1" />
        <PackageReference Include="MudBlazor" Version="7.18.0" />
        <PackageReference Include="System.Net.Http.Json" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
        <PackageReference Include="Finbuckle.MultiTenant.AspNetCore" Version="7.0.2" />
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.2" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\SmsGateway.Core\SmsGateway.Core.csproj" />
    </ItemGroup>

</Project>
