{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ApiSettings": {"BaseUrl": "https://localhost:7001"}, "JwtSettings": {"Issuer": "SmsGateway", "Audience": "SmsGateway.Portal", "SecretKey": "your-super-secret-key-here-make-it-long-and-complex-for-production", "ExpiryMinutes": 60}, "MultiTenant": {"DefaultTenant": {"Id": "default", "Identifier": "default", "Name": "<PERSON><PERSON><PERSON>"}}}