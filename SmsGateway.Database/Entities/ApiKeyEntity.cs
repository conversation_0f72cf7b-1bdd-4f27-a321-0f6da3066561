using System.ComponentModel.DataAnnotations;
using Finbuckle.MultiTenant;

namespace SmsGateway.Database.Entities;

/// <summary>
/// API Key entity for API authentication
/// </summary>
[MultiTenant]
public class ApiKeyEntity
{
    /// <summary>
    /// Unique API key identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// User ID that owns this API key
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// API key name/description
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Hashed API key value
    /// </summary>
    [Required]
    public string KeyHash { get; set; } = string.Empty;

    /// <summary>
    /// API key prefix (first 8 characters for identification)
    /// </summary>
    [Required]
    [MaxLength(8)]
    public string KeyPrefix { get; set; } = string.Empty;

    /// <summary>
    /// Whether the API key is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// API key expiry date (null = never expires)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Last time this API key was used
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// IP address of last usage
    /// </summary>
    [MaxLength(45)]
    public string? LastUsedIp { get; set; }

    /// <summary>
    /// Number of times this API key has been used
    /// </summary>
    public long UsageCount { get; set; } = 0;

    /// <summary>
    /// Rate limit: requests per minute (null = no limit)
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Allowed IP addresses (comma-separated, null = any IP)
    /// </summary>
    public string? AllowedIpAddresses { get; set; }

    /// <summary>
    /// Allowed permissions for this API key (JSON array)
    /// </summary>
    public string? Permissions { get; set; }

    /// <summary>
    /// Additional metadata (JSON)
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// When the API key was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the API key was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the API key
    /// </summary>
    [MaxLength(100)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Navigation property to user
    /// </summary>
    public virtual UserEntity User { get; set; } = null!;

    /// <summary>
    /// Check if the API key is expired
    /// </summary>
    public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow;

    /// <summary>
    /// Check if the API key is valid (active and not expired)
    /// </summary>
    public bool IsValid => IsActive && !IsExpired;
}

/// <summary>
/// User session entity for tracking active sessions
/// </summary>
[MultiTenant]
public class UserSessionEntity
{
    /// <summary>
    /// Unique session identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// User ID
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// JWT token ID (jti claim)
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string TokenId { get; set; } = string.Empty;

    /// <summary>
    /// Refresh token (hashed)
    /// </summary>
    public string? RefreshTokenHash { get; set; }

    /// <summary>
    /// Session start time
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Session expiry time
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Last activity time
    /// </summary>
    public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// IP address of the session
    /// </summary>
    [MaxLength(45)]
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent string
    /// </summary>
    [MaxLength(500)]
    public string? UserAgent { get; set; }

    /// <summary>
    /// Whether the session is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Session metadata (JSON)
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Navigation property to user
    /// </summary>
    public virtual UserEntity User { get; set; } = null!;

    /// <summary>
    /// Check if the session is expired
    /// </summary>
    public bool IsExpired => ExpiresAt <= DateTime.UtcNow;

    /// <summary>
    /// Check if the session is valid (active and not expired)
    /// </summary>
    public bool IsValid => IsActive && !IsExpired;
}
