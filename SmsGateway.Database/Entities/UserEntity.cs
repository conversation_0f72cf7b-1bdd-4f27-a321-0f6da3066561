using System.ComponentModel.DataAnnotations;
using Finbuckle.MultiTenant;

namespace SmsGateway.Database.Entities;

/// <summary>
/// User entity for authentication and authorization
/// </summary>
[MultiTenant]
public class UserEntity
{
    /// <summary>
    /// Unique user identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Username for login
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Hashed password
    /// </summary>
    [Required]
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// First name
    /// </summary>
    [MaxLength(100)]
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name
    /// </summary>
    [MaxLength(100)]
    public string? LastName { get; set; }

    /// <summary>
    /// Whether the user account is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether the email is verified
    /// </summary>
    public bool EmailVerified { get; set; } = false;

    /// <summary>
    /// Email verification token
    /// </summary>
    public string? EmailVerificationToken { get; set; }

    /// <summary>
    /// Password reset token
    /// </summary>
    public string? PasswordResetToken { get; set; }

    /// <summary>
    /// Password reset token expiry
    /// </summary>
    public DateTime? PasswordResetTokenExpiry { get; set; }

    /// <summary>
    /// Last login timestamp
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// Last login IP address
    /// </summary>
    [MaxLength(45)]
    public string? LastLoginIp { get; set; }

    /// <summary>
    /// Failed login attempts count
    /// </summary>
    public int FailedLoginAttempts { get; set; } = 0;

    /// <summary>
    /// Account locked until (if locked)
    /// </summary>
    public DateTime? LockedUntil { get; set; }

    /// <summary>
    /// User preferences (JSON)
    /// </summary>
    public string? Preferences { get; set; }

    /// <summary>
    /// User timezone
    /// </summary>
    [MaxLength(50)]
    public string TimeZone { get; set; } = "UTC";

    /// <summary>
    /// User language/locale
    /// </summary>
    [MaxLength(10)]
    public string Language { get; set; } = "en";

    /// <summary>
    /// When the user was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the user was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the user
    /// </summary>
    [MaxLength(100)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Navigation property to user roles
    /// </summary>
    public virtual ICollection<UserRoleEntity> UserRoles { get; set; } = new List<UserRoleEntity>();

    /// <summary>
    /// Navigation property to API keys
    /// </summary>
    public virtual ICollection<ApiKeyEntity> ApiKeys { get; set; } = new List<ApiKeyEntity>();

    /// <summary>
    /// Navigation property to user sessions
    /// </summary>
    public virtual ICollection<UserSessionEntity> UserSessions { get; set; } = new List<UserSessionEntity>();

    /// <summary>
    /// Full name (computed property)
    /// </summary>
    public string FullName => $"{FirstName} {LastName}".Trim();
}
