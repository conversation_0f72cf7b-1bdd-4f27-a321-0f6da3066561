using Microsoft.EntityFrameworkCore;
using Finbuckle.MultiTenant;
using SmsGateway.Database.Entities;
using SmsGateway.Core.Models.Common;

namespace SmsGateway.Database;

/// <summary>
/// Entity Framework DbContext for SMS Gateway
/// </summary>
public class SmsGatewayDbContext : MultiTenantDbContext
{
    public SmsGatewayDbContext(ITenantInfo tenantInfo) : base(tenantInfo)
    {
    }

    public SmsGatewayDbContext(ITenantInfo tenantInfo, DbContextOptions<SmsGatewayDbContext> options) : base(tenantInfo, options)
    {
    }

    // SMS-related entities
    public DbSet<SmsMessageEntity> SmsMessages { get; set; }
    public DbSet<SmsTemplateEntity> SmsTemplates { get; set; }
    public DbSet<SmsStatusHistoryEntity> SmsStatusHistory { get; set; }
    public DbSet<SmsQueueEntity> SmsQueue { get; set; }

    // Authentication entities
    public DbSet<UserEntity> Users { get; set; }
    public DbSet<RoleEntity> Roles { get; set; }
    public DbSet<UserRoleEntity> UserRoles { get; set; }
    public DbSet<ApiKeyEntity> ApiKeys { get; set; }
    public DbSet<UserSessionEntity> UserSessions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure multi-tenancy
        modelBuilder.ConfigureMultiTenant();

        ConfigureSmsEntities(modelBuilder);
        ConfigureAuthenticationEntities(modelBuilder);
    }

    private void ConfigureSmsEntities(ModelBuilder modelBuilder)
    {
        // Configure SmsMessageEntity
        modelBuilder.Entity<SmsMessageEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => e.ExternalMessageId);
            entity.HasIndex(e => e.To);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.ScheduledAt);
            entity.HasIndex(e => new { e.TenantId, e.Status });
            entity.HasIndex(e => new { e.TenantId, e.CreatedAt });

            entity.Property(e => e.OriginalMessage).IsRequired();
            entity.Property(e => e.ProcessedMessage).IsRequired();
            entity.Property(e => e.To).IsRequired().HasMaxLength(20);
            entity.Property(e => e.From).HasMaxLength(20);
            entity.Property(e => e.Provider).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Currency).HasMaxLength(3);
            entity.Property(e => e.ErrorCode).HasMaxLength(50);
            entity.Property(e => e.Reference).HasMaxLength(255);
            entity.Property(e => e.OriginIpAddress).HasMaxLength(45);
            entity.Property(e => e.UserAgent).HasMaxLength(500);
            entity.Property(e => e.Cost).HasPrecision(10, 4);

            // Configure relationships
            entity.HasOne(e => e.Template)
                  .WithMany(t => t.Messages)
                  .HasForeignKey(e => e.TemplateId)
                  .OnDelete(DeleteBehavior.SetNull);

            entity.HasMany(e => e.StatusHistory)
                  .WithOne(h => h.SmsMessage)
                  .HasForeignKey(h => h.SmsMessageId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure SmsTemplateEntity
        modelBuilder.Entity<SmsTemplateEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => new { e.TenantId, e.Name }).IsUnique();
            entity.HasIndex(e => e.Category);
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => new { e.TenantId, e.IsActive });

            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.DisplayName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Content).IsRequired();
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.Language).HasMaxLength(10);
            entity.Property(e => e.Tags).HasMaxLength(500);
            entity.Property(e => e.DefaultSenderId).HasMaxLength(20);
            entity.Property(e => e.PreferredProvider).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);
        });

        // Configure SmsStatusHistoryEntity
        modelBuilder.Entity<SmsStatusHistoryEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => e.SmsMessageId);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => new { e.SmsMessageId, e.CreatedAt });

            entity.Property(e => e.Reason).HasMaxLength(500);
            entity.Property(e => e.ErrorCode).HasMaxLength(50);
        });

        // Configure SmsQueueEntity
        modelBuilder.Entity<SmsQueueEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => e.SmsMessageId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.ProcessAt);
            entity.HasIndex(e => new { e.Status, e.ProcessAt });
            entity.HasIndex(e => new { e.TenantId, e.Status });
            entity.HasIndex(e => e.HangfireJobId);

            entity.Property(e => e.HangfireJobId).HasMaxLength(100);

            // Configure relationship
            entity.HasOne(e => e.SmsMessage)
                  .WithMany()
                  .HasForeignKey(e => e.SmsMessageId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure enum conversions
        modelBuilder.Entity<SmsMessageEntity>()
            .Property(e => e.Status)
            .HasConversion<int>();

        modelBuilder.Entity<SmsMessageEntity>()
            .Property(e => e.Priority)
            .HasConversion<int>();

        modelBuilder.Entity<SmsStatusHistoryEntity>()
            .Property(e => e.PreviousStatus)
            .HasConversion<int?>();

        modelBuilder.Entity<SmsStatusHistoryEntity>()
            .Property(e => e.NewStatus)
            .HasConversion<int>();

        modelBuilder.Entity<SmsQueueEntity>()
            .Property(e => e.Status)
            .HasConversion<int>();
    }

    private void ConfigureAuthenticationEntities(ModelBuilder modelBuilder)
    {
        // Configure UserEntity
        modelBuilder.Entity<UserEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => new { e.TenantId, e.Username }).IsUnique();
            entity.HasIndex(e => new { e.TenantId, e.Email }).IsUnique();
            entity.HasIndex(e => e.EmailVerificationToken);
            entity.HasIndex(e => e.PasswordResetToken);

            entity.Property(e => e.Username).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
            entity.Property(e => e.PasswordHash).IsRequired();
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.LastLoginIp).HasMaxLength(45);
            entity.Property(e => e.TimeZone).HasMaxLength(50);
            entity.Property(e => e.Language).HasMaxLength(10);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);

            entity.HasMany(e => e.UserRoles)
                  .WithOne(ur => ur.User)
                  .HasForeignKey(ur => ur.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.ApiKeys)
                  .WithOne(ak => ak.User)
                  .HasForeignKey(ak => ak.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.UserSessions)
                  .WithOne(us => us.User)
                  .HasForeignKey(us => us.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure RoleEntity
        modelBuilder.Entity<RoleEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => new { e.TenantId, e.Name }).IsUnique();

            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.DisplayName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);

            entity.HasMany(e => e.UserRoles)
                  .WithOne(ur => ur.Role)
                  .HasForeignKey(ur => ur.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure UserRoleEntity
        modelBuilder.Entity<UserRoleEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => new { e.UserId, e.RoleId }).IsUnique();

            entity.Property(e => e.AssignedBy).HasMaxLength(100);
        });

        // Configure ApiKeyEntity
        modelBuilder.Entity<ApiKeyEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.KeyPrefix);
            entity.HasIndex(e => e.IsActive);

            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.KeyHash).IsRequired();
            entity.Property(e => e.KeyPrefix).IsRequired().HasMaxLength(8);
            entity.Property(e => e.LastUsedIp).HasMaxLength(45);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
        });

        // Configure UserSessionEntity
        modelBuilder.Entity<UserSessionEntity>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.TokenId).IsUnique();
            entity.HasIndex(e => e.ExpiresAt);
            entity.HasIndex(e => e.IsActive);

            entity.Property(e => e.TokenId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.UserAgent).HasMaxLength(500);
        });
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update timestamps
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            switch (entry.Entity)
            {
                case SmsMessageEntity message:
                    if (entry.State == EntityState.Added)
                        message.CreatedAt = DateTime.UtcNow;
                    message.UpdatedAt = DateTime.UtcNow;
                    break;

                case SmsTemplateEntity template:
                    if (entry.State == EntityState.Added)
                        template.CreatedAt = DateTime.UtcNow;
                    template.UpdatedAt = DateTime.UtcNow;
                    break;

                case SmsQueueEntity queue:
                    if (entry.State == EntityState.Added)
                        queue.CreatedAt = DateTime.UtcNow;
                    queue.UpdatedAt = DateTime.UtcNow;
                    break;

                case UserEntity user:
                    if (entry.State == EntityState.Added)
                        user.CreatedAt = DateTime.UtcNow;
                    user.UpdatedAt = DateTime.UtcNow;
                    break;

                case RoleEntity role:
                    if (entry.State == EntityState.Added)
                        role.CreatedAt = DateTime.UtcNow;
                    role.UpdatedAt = DateTime.UtcNow;
                    break;

                case ApiKeyEntity apiKey:
                    if (entry.State == EntityState.Added)
                        apiKey.CreatedAt = DateTime.UtcNow;
                    apiKey.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
