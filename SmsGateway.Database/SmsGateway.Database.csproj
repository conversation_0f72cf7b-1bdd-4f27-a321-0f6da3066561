<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0"/>
        <PackageReference Include="Finbuckle.MultiTenant.EntityFrameworkCore" Version="7.0.2"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\SmsGateway.Core\SmsGateway.Core.csproj" />
    </ItemGroup>

</Project>
