using System.Collections.Concurrent;
using System.Reflection;
using System.Runtime.Loader;
using System.Security.Cryptography;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmsGateway.Core.Interfaces;
using SmsGateway.Core.Models;
using SmsGateway.Core.Models.Common;

namespace SmsGateway.Data.Services;

/// <summary>
/// Enhanced plugin manager with runtime loading/unloading capabilities
/// </summary>
public class EnhancedPluginManager : IPluginManager
{
    private readonly ConcurrentDictionary<string, ISmsProvider> _providers = new();
    private readonly ConcurrentDictionary<string, ProviderConfiguration> _configurations = new();
    private readonly ConcurrentDictionary<string, ProviderStatistics> _statistics = new();
    private readonly ConcurrentDictionary<string, LoadedPluginInfo> _loadedPlugins = new();
    private readonly ConcurrentDictionary<string, PluginAssemblyLoadContext> _loadContexts = new();
    private readonly ILogger<EnhancedPluginManager> _logger;
    private readonly IConfiguration _configuration;
    private readonly FileSystemWatcher? _pluginWatcher;

    public EnhancedPluginManager(ILogger<EnhancedPluginManager> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;

        // Set up file system watcher for hot-reloading
        var pluginsPath = GetPluginsPath();
        if (Directory.Exists(pluginsPath))
        {
            _pluginWatcher = new FileSystemWatcher(pluginsPath, "*.dll")
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime,
                EnableRaisingEvents = true
            };
            _pluginWatcher.Changed += OnPluginFileChanged;
            _pluginWatcher.Created += OnPluginFileChanged;
        }
    }

    public async Task LoadPluginsAsync()
    {
        _logger.LogInformation("Loading SMS provider plugins...");

        var pluginsPath = GetPluginsPath();
        if (!Directory.Exists(pluginsPath))
        {
            Directory.CreateDirectory(pluginsPath);
            _logger.LogInformation("Created plugins directory: {PluginsPath}", pluginsPath);
        }

        var pluginFiles = await ScanForPluginsAsync();
        var loadTasks = pluginFiles.Select(LoadPluginAsync);
        var results = await Task.WhenAll(loadTasks);

        var successCount = results.Count(r => r.Success);
        _logger.LogInformation("Loaded {SuccessCount}/{TotalCount} SMS provider plugins", successCount, results.Length);
    }

    public async Task<PluginLoadResult> LoadPluginAsync(string pluginPath)
    {
        var result = new PluginLoadResult();

        try
        {
            if (!File.Exists(pluginPath))
            {
                result.Errors.Add($"Plugin file not found: {pluginPath}");
                return result;
            }

            var pluginName = Path.GetFileNameWithoutExtension(pluginPath);
            
            // Check if plugin is already loaded
            if (_loadedPlugins.ContainsKey(pluginName))
            {
                result.Warnings.Add($"Plugin {pluginName} is already loaded");
                return result;
            }

            // Create plugin metadata
            var metadata = await CreatePluginMetadataAsync(pluginPath);
            result.Metadata = metadata;

            // Create isolated assembly load context
            var loadContext = new PluginAssemblyLoadContext(pluginPath);
            var assembly = loadContext.LoadFromAssemblyPath(pluginPath);

            // Find plugin factory
            var factoryType = assembly.GetTypes()
                .FirstOrDefault(t => typeof(IPluginFactory).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract);

            if (factoryType == null)
            {
                result.Errors.Add($"No plugin factory found in {pluginName}");
                return result;
            }

            var factory = Activator.CreateInstance(factoryType) as IPluginFactory;
            if (factory == null)
            {
                result.Errors.Add($"Failed to create plugin factory instance for {pluginName}");
                return result;
            }

            // Validate dependencies
            var dependencyValidation = factory.ValidateDependencies();
            if (!dependencyValidation.IsValid)
            {
                result.Errors.AddRange(dependencyValidation.Errors);
                return result;
            }

            // Create providers
            var providers = factory.CreateProviders().ToList();
            if (!providers.Any())
            {
                result.Warnings.Add($"No providers found in plugin {pluginName}");
            }

            // Register providers
            foreach (var provider in providers)
            {
                var configSection = _configuration.GetSection($"SmsProviders:{provider.Name}");
                if (configSection.Exists())
                {
                    var config = CreateConfigurationFromSection(configSection);
                    if (config != null)
                    {
                        await RegisterProviderAsync(provider, config);
                        result.LoadedProviders.Add(provider.Name);
                    }
                }
                else
                {
                    result.Warnings.Add($"No configuration found for provider {provider.Name}");
                }
            }

            // Store plugin info
            var pluginInfo = new LoadedPluginInfo
            {
                Metadata = metadata,
                AssemblyLoadContext = new WeakReference(loadContext),
                ProviderNames = result.LoadedProviders,
                LoadErrors = result.Errors
            };

            _loadedPlugins.TryAdd(pluginName, pluginInfo);
            _loadContexts.TryAdd(pluginName, loadContext);

            result.Success = true;
            result.PluginName = pluginName;

            _logger.LogInformation("Successfully loaded plugin: {PluginName} with {ProviderCount} providers", 
                pluginName, result.LoadedProviders.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load plugin from {PluginPath}", pluginPath);
            result.Errors.Add($"Exception loading plugin: {ex.Message}");
        }

        return result;
    }

    public async Task<bool> UnloadPluginAsync(string pluginName)
    {
        try
        {
            if (!_loadedPlugins.TryGetValue(pluginName, out var pluginInfo))
            {
                _logger.LogWarning("Plugin {PluginName} not found for unloading", pluginName);
                return false;
            }

            // Unregister all providers from this plugin
            foreach (var providerName in pluginInfo.ProviderNames)
            {
                await UnregisterProviderAsync(providerName);
            }

            // Dispose providers if they implement IAsyncDisposable
            foreach (var providerName in pluginInfo.ProviderNames)
            {
                if (_providers.TryGetValue(providerName, out var provider))
                {
                    await provider.DisposeAsync();
                }
            }

            // Unload assembly context
            if (_loadContexts.TryRemove(pluginName, out var loadContext))
            {
                loadContext.Unload();
            }

            // Remove plugin info
            _loadedPlugins.TryRemove(pluginName, out _);

            _logger.LogInformation("Successfully unloaded plugin: {PluginName}", pluginName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unload plugin {PluginName}", pluginName);
            return false;
        }
    }

    public async Task<PluginLoadResult> ReloadPluginAsync(string pluginName)
    {
        if (!_loadedPlugins.TryGetValue(pluginName, out var pluginInfo))
        {
            return new PluginLoadResult
            {
                Success = false,
                Errors = { $"Plugin {pluginName} not found" }
            };
        }

        var pluginPath = pluginInfo.Metadata.AssemblyPath;
        
        // Unload existing plugin
        await UnloadPluginAsync(pluginName);

        // Load plugin again
        return await LoadPluginAsync(pluginPath);
    }

    public async Task RegisterProviderAsync(ISmsProvider provider, ProviderConfiguration configuration)
    {
        try
        {
            // Validate configuration
            var validationResult = await provider.ValidateConfigurationAsync(configuration);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"Invalid configuration for provider {provider.Name}: {string.Join(", ", validationResult.Errors)}");
            }

            // Initialize provider
            await provider.InitializeAsync(configuration);

            // Register provider
            _providers.AddOrUpdate(provider.Name, provider, (key, oldValue) => provider);
            _configurations.AddOrUpdate(provider.Name, configuration, (key, oldValue) => configuration);
            
            // Initialize statistics
            _statistics.AddOrUpdate(provider.Name, new ProviderStatistics
            {
                ProviderName = provider.Name,
                IsHealthy = provider.IsAvailable
            }, (key, oldValue) => oldValue);

            _logger.LogInformation("Registered SMS provider: {ProviderName} ({DisplayName})", provider.Name, provider.DisplayName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register provider {ProviderName}", provider.Name);
            throw;
        }
    }

    public async Task UnregisterProviderAsync(string providerName)
    {
        if (_providers.TryRemove(providerName, out var provider))
        {
            _configurations.TryRemove(providerName, out _);
            _statistics.TryRemove(providerName, out _);
            
            _logger.LogInformation("Unregistered SMS provider: {ProviderName}", providerName);
        }
        
        await Task.CompletedTask;
    }

    // Implement other IPluginManager methods...
    public ISmsProvider? GetProvider(string providerName)
    {
        _providers.TryGetValue(providerName, out var provider);
        return provider;
    }

    public IEnumerable<ISmsProvider> GetAllProviders()
    {
        return _providers.Values;
    }

    public IEnumerable<ISmsProvider> GetEnabledProviders()
    {
        return _providers.Values
            .Where(p => IsProviderEnabled(p.Name) && p.IsAvailable)
            .OrderBy(p => _configurations.TryGetValue(p.Name, out var config) ? config.Priority : int.MaxValue);
    }

    public ISmsProvider? GetBestProvider(SmsRequest request)
    {
        var enabledProviders = GetEnabledProviders();
        
        // If specific provider requested, try to use it
        if (!string.IsNullOrEmpty(request.Provider))
        {
            var requestedProvider = enabledProviders.FirstOrDefault(p => p.Name.Equals(request.Provider, StringComparison.OrdinalIgnoreCase));
            if (requestedProvider != null)
            {
                return requestedProvider;
            }
        }

        // Extract country code from phone number (basic implementation)
        var countryCode = ExtractCountryCode(request.To);
        
        // Find providers that support the country
        var suitableProviders = enabledProviders
            .Where(p => string.IsNullOrEmpty(countryCode) || p.SupportsCountry(countryCode))
            .ToList();

        // Return the highest priority (lowest priority number) provider
        return suitableProviders.FirstOrDefault();
    }

    public ProviderConfiguration? GetProviderConfiguration(string providerName)
    {
        _configurations.TryGetValue(providerName, out var configuration);
        return configuration;
    }

    public async Task UpdateProviderConfigurationAsync(string providerName, ProviderConfiguration configuration)
    {
        if (_providers.TryGetValue(providerName, out var provider))
        {
            var validationResult = await provider.ValidateConfigurationAsync(configuration);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"Invalid configuration: {string.Join(", ", validationResult.Errors)}");
            }

            await provider.InitializeAsync(configuration);
            _configurations.AddOrUpdate(providerName, configuration, (key, oldValue) => configuration);
            
            _logger.LogInformation("Updated configuration for provider: {ProviderName}", providerName);
        }
        else
        {
            throw new ArgumentException($"Provider {providerName} not found");
        }
    }

    public bool IsProviderEnabled(string providerName)
    {
        if (_configurations.TryGetValue(providerName, out var config))
        {
            return config.Enabled;
        }
        return false;
    }

    public ProviderStatistics? GetProviderStatistics(string providerName)
    {
        _statistics.TryGetValue(providerName, out var statistics);
        return statistics;
    }

    public IEnumerable<LoadedPluginInfo> GetLoadedPlugins()
    {
        return _loadedPlugins.Values;
    }

    public LoadedPluginInfo? GetPluginInfo(string pluginName)
    {
        _loadedPlugins.TryGetValue(pluginName, out var pluginInfo);
        return pluginInfo;
    }

    public async Task<IEnumerable<string>> ScanForPluginsAsync()
    {
        var pluginsPath = GetPluginsPath();
        if (!Directory.Exists(pluginsPath))
        {
            return Enumerable.Empty<string>();
        }

        var dllFiles = Directory.GetFiles(pluginsPath, "*.dll", SearchOption.AllDirectories);
        return dllFiles;
    }

    public void UpdateStatistics(string providerName, bool success, TimeSpan responseTime, string? error = null)
    {
        _statistics.AddOrUpdate(providerName, 
            new ProviderStatistics
            {
                ProviderName = providerName,
                TotalMessagesSent = 1,
                SuccessfulMessages = success ? 1 : 0,
                FailedMessages = success ? 0 : 1,
                AverageResponseTime = responseTime,
                LastUsed = DateTime.UtcNow,
                IsHealthy = success,
                LastError = error
            },
            (key, existing) =>
            {
                existing.TotalMessagesSent++;
                if (success)
                    existing.SuccessfulMessages++;
                else
                    existing.FailedMessages++;
                
                // Update average response time
                existing.AverageResponseTime = TimeSpan.FromMilliseconds(
                    (existing.AverageResponseTime.TotalMilliseconds + responseTime.TotalMilliseconds) / 2);
                
                existing.LastUsed = DateTime.UtcNow;
                existing.IsHealthy = success;
                if (!success && !string.IsNullOrEmpty(error))
                    existing.LastError = error;
                
                return existing;
            });
    }

    private string GetPluginsPath()
    {
        return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Plugins");
    }

    private async Task<PluginMetadata> CreatePluginMetadataAsync(string pluginPath)
    {
        var fileInfo = new FileInfo(pluginPath);
        var fileHash = await ComputeFileHashAsync(pluginPath);

        return new PluginMetadata
        {
            AssemblyPath = pluginPath,
            AssemblyName = Path.GetFileNameWithoutExtension(pluginPath),
            FileSize = fileInfo.Length,
            LastModified = fileInfo.LastWriteTimeUtc,
            FileHash = fileHash,
            LoadedAt = DateTime.UtcNow,
            IsLoaded = true
        };
    }

    private async Task<string> ComputeFileHashAsync(string filePath)
    {
        using var sha256 = SHA256.Create();
        using var stream = File.OpenRead(filePath);
        var hash = await sha256.ComputeHashAsync(stream);
        return Convert.ToHexString(hash);
    }

    private ProviderConfiguration? CreateConfigurationFromSection(IConfigurationSection section)
    {
        var type = section["Type"];
        return type?.ToLowerInvariant() switch
        {
            "apikey" => section.Get<ApiKeyProviderConfiguration>(),
            "usernamepassword" => section.Get<UsernamePasswordProviderConfiguration>(),
            _ => section.Get<ProviderConfiguration>()
        };
    }

    private string? ExtractCountryCode(string phoneNumber)
    {
        // Basic country code extraction - in production, use a proper phone number library
        if (phoneNumber.StartsWith("+"))
        {
            if (phoneNumber.StartsWith("+1")) return "US";
            if (phoneNumber.StartsWith("+44")) return "GB";
            if (phoneNumber.StartsWith("+49")) return "DE";
            // Add more country codes as needed
        }
        return null;
    }

    private async void OnPluginFileChanged(object sender, FileSystemEventArgs e)
    {
        try
        {
            // Debounce file changes
            await Task.Delay(1000);

            var pluginName = Path.GetFileNameWithoutExtension(e.FullPath);
            _logger.LogInformation("Plugin file changed: {PluginName}, attempting reload...", pluginName);

            if (_loadedPlugins.ContainsKey(pluginName))
            {
                await ReloadPluginAsync(pluginName);
            }
            else
            {
                await LoadPluginAsync(e.FullPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle plugin file change for {PluginPath}", e.FullPath);
        }
    }

    public void Dispose()
    {
        _pluginWatcher?.Dispose();
        
        // Unload all plugins
        var pluginNames = _loadedPlugins.Keys.ToList();
        foreach (var pluginName in pluginNames)
        {
            UnloadPluginAsync(pluginName).Wait();
        }
    }
}

/// <summary>
/// Custom assembly load context for plugin isolation
/// </summary>
public class PluginAssemblyLoadContext : AssemblyLoadContext
{
    private readonly AssemblyDependencyResolver _resolver;

    public PluginAssemblyLoadContext(string pluginPath) : base(isCollectible: true)
    {
        _resolver = new AssemblyDependencyResolver(pluginPath);
    }

    protected override Assembly? Load(AssemblyName assemblyName)
    {
        var assemblyPath = _resolver.ResolveAssemblyToPath(assemblyName);
        if (assemblyPath != null)
        {
            return LoadFromAssemblyPath(assemblyPath);
        }

        return null;
    }

    protected override IntPtr LoadUnmanagedDll(string unmanagedDllName)
    {
        var libraryPath = _resolver.ResolveUnmanagedDllToPath(unmanagedDllName);
        if (libraryPath != null)
        {
            return LoadUnmanagedDllFromPath(libraryPath);
        }

        return IntPtr.Zero;
    }
}
