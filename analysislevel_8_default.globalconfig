# NOTE: Requires **VS2019 16.7** or later

# Rules from '8.0' release with 'Default' analysis mode
# Description: Rules with enabled-by-default state from '8.0' release with 'Default' analysis mode. Rules that are first released in a version later than '8.0' are disabled.

is_global = true

global_level = -100


# CA1514: Avoid redundant length argument
dotnet_diagnostic.CA1514.severity = none

# CA1871: Do not pass a nullable struct to 'ArgumentNullException.ThrowIfNull'
dotnet_diagnostic.CA1871.severity = none

# CA1872: Prefer 'Convert.ToHexString' and 'Convert.ToHexStringLower' over call chains based on 'BitConverter.ToString'
dotnet_diagnostic.CA1872.severity = none

# CA1873: Avoid potentially expensive logging
dotnet_diagnostic.CA1873.severity = none

# CA1874: Use 'Regex.IsMatch'
dotnet_diagnostic.CA1874.severity = none

# CA1875: Use 'Regex.Count'
dotnet_diagnostic.CA1875.severity = none

# CA2022: Avoid inexact read with 'Stream.Read'
dotnet_diagnostic.CA2022.severity = none

# CA2023: Invalid braces in message template
dotnet_diagnostic.CA2023.severity = none

# CA2024: Do not use 'StreamReader.EndOfStream' in async methods
dotnet_diagnostic.CA2024.severity = none

# CA2262: Set 'MaxResponseHeadersLength' properly
dotnet_diagnostic.CA2262.severity = none

# CA2263: Prefer generic overload when type is known
dotnet_diagnostic.CA2263.severity = none

# CA2264: Do not pass a non-nullable value to 'ArgumentNullException.ThrowIfNull'
dotnet_diagnostic.CA2264.severity = none

# CA2265: Do not compare Span<T> to 'null' or 'default'
dotnet_diagnostic.CA2265.severity = none
