<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <PackageId>SmsGateway.Core</PackageId>
        <PackageVersion>1.0.0</PackageVersion>
        <Authors>SMS Gateway Team</Authors>
        <Description>Core interfaces and models for SMS Gateway plugin system</Description>
        <PackageTags>sms;gateway;plugin;core</PackageTags>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Finbuckle.MultiTenant" Version="7.0.2" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    </ItemGroup>

</Project>
