namespace SmsGateway.Core.Models;

/// <summary>
/// Metadata about a plugin
/// </summary>
public class PluginMetadata
{
    /// <summary>
    /// Plugin assembly file path
    /// </summary>
    public string AssemblyPath { get; set; } = string.Empty;

    /// <summary>
    /// Plugin assembly name
    /// </summary>
    public string AssemblyName { get; set; } = string.Empty;

    /// <summary>
    /// Plugin version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Plugin author
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// Plugin description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// When the plugin was loaded
    /// </summary>
    public DateTime LoadedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Whether the plugin is currently loaded
    /// </summary>
    public bool IsLoaded { get; set; }

    /// <summary>
    /// File hash for integrity checking
    /// </summary>
    public string? FileHash { get; set; }

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// Last modified time of the plugin file
    /// </summary>
    public DateTime LastModified { get; set; }

    /// <summary>
    /// Dependencies required by this plugin
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// Minimum core version required
    /// </summary>
    public string? MinimumCoreVersion { get; set; }

    /// <summary>
    /// Plugin configuration schema (JSON schema)
    /// </summary>
    public string? ConfigurationSchema { get; set; }

    /// <summary>
    /// Plugin tags for categorization
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Information about a loaded plugin
/// </summary>
public class LoadedPluginInfo
{
    /// <summary>
    /// Plugin metadata
    /// </summary>
    public PluginMetadata Metadata { get; set; } = new();

    /// <summary>
    /// Assembly load context (for unloading)
    /// </summary>
    public WeakReference? AssemblyLoadContext { get; set; }

    /// <summary>
    /// Provider instances from this plugin
    /// </summary>
    public List<string> ProviderNames { get; set; } = new();

    /// <summary>
    /// Load errors if any
    /// </summary>
    public List<string> LoadErrors { get; set; } = new();

    /// <summary>
    /// Whether the plugin loaded successfully
    /// </summary>
    public bool LoadedSuccessfully => LoadErrors.Count == 0;
}
