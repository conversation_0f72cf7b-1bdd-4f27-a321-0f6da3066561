using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Abstractions;

namespace SmsGateway.Core.Models.MultiTenancy;

/// <summary>
/// Tenant information for multi-tenancy support
/// </summary>
public class TenantInfo : ITenantInfo
{
    /// <summary>
    /// Unique tenant identifier
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Tenant identifier used in URLs, headers, etc.
    /// </summary>
    public string Identifier { get; set; } = string.Empty;

    /// <summary>
    /// Display name for the tenant
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Database connection string for this tenant
    /// </summary>
    public string? ConnectionString { get; set; }

    /// <summary>
    /// Whether the tenant is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Tenant creation date
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Tenant settings (JSON)
    /// </summary>
    public string? Settings { get; set; }

    /// <summary>
    /// Tenant subscription plan
    /// </summary>
    public string? SubscriptionPlan { get; set; }

    /// <summary>
    /// Maximum SMS messages per month for this tenant
    /// </summary>
    public int? MonthlyMessageLimit { get; set; }

    /// <summary>
    /// Current month's message count
    /// </summary>
    public int CurrentMonthMessageCount { get; set; }

    /// <summary>
    /// Tenant contact email
    /// </summary>
    public string? ContactEmail { get; set; }

    /// <summary>
    /// Tenant API key for authentication
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// Rate limiting: requests per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Allowed IP addresses (comma-separated)
    /// </summary>
    public string? AllowedIpAddresses { get; set; }

    /// <summary>
    /// Webhook URL for delivery notifications
    /// </summary>
    public string? WebhookUrl { get; set; }

    /// <summary>
    /// Default SMS provider for this tenant
    /// </summary>
    public string? DefaultProvider { get; set; }

    /// <summary>
    /// Tenant-specific provider configurations (JSON)
    /// </summary>
    public string? ProviderConfigurations { get; set; }
}
