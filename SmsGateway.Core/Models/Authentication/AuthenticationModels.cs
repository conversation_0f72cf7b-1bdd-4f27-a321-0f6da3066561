using System.ComponentModel.DataAnnotations;

namespace SmsGateway.Core.Models.Authentication;

/// <summary>
/// Login request model
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Username or email
    /// </summary>
    [Required]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password
    /// </summary>
    [Required]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Remember me flag
    /// </summary>
    public bool RememberMe { get; set; } = false;

    /// <summary>
    /// Tenant identifier (for multi-tenant login)
    /// </summary>
    public string? TenantId { get; set; }
}

/// <summary>
/// Login response model
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// Whether login was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// JWT access token
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// Refresh token
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// Token expiry time
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// User information
    /// </summary>
    public UserInfo? User { get; set; }

    /// <summary>
    /// Error message if login failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether account is locked
    /// </summary>
    public bool IsLocked { get; set; }

    /// <summary>
    /// Lock expiry time if account is locked
    /// </summary>
    public DateTime? LockedUntil { get; set; }
}

/// <summary>
/// User registration request model
/// </summary>
public class RegisterRequest
{
    /// <summary>
    /// Username
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    [Required]
    [EmailAddress]
    [MaxLength(255)]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Password
    /// </summary>
    [Required]
    [MinLength(8)]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Password confirmation
    /// </summary>
    [Required]
    [Compare(nameof(Password))]
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// First name
    /// </summary>
    [MaxLength(100)]
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name
    /// </summary>
    [MaxLength(100)]
    public string? LastName { get; set; }

    /// <summary>
    /// Tenant identifier (for multi-tenant registration)
    /// </summary>
    public string? TenantId { get; set; }
}

/// <summary>
/// User information model
/// </summary>
public class UserInfo
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// First name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Whether email is verified
    /// </summary>
    public bool EmailVerified { get; set; }

    /// <summary>
    /// User roles
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// User permissions
    /// </summary>
    public List<string> Permissions { get; set; } = new();

    /// <summary>
    /// Tenant ID
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// User timezone
    /// </summary>
    public string TimeZone { get; set; } = "UTC";

    /// <summary>
    /// User language
    /// </summary>
    public string Language { get; set; } = "en";
}

/// <summary>
/// Refresh token request model
/// </summary>
public class RefreshTokenRequest
{
    /// <summary>
    /// Refresh token
    /// </summary>
    [Required]
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// Change password request model
/// </summary>
public class ChangePasswordRequest
{
    /// <summary>
    /// Current password
    /// </summary>
    [Required]
    public string CurrentPassword { get; set; } = string.Empty;

    /// <summary>
    /// New password
    /// </summary>
    [Required]
    [MinLength(8)]
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// New password confirmation
    /// </summary>
    [Required]
    [Compare(nameof(NewPassword))]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// Reset password request model
/// </summary>
public class ResetPasswordRequest
{
    /// <summary>
    /// Email address
    /// </summary>
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Tenant identifier
    /// </summary>
    public string? TenantId { get; set; }
}

/// <summary>
/// Confirm password reset request model
/// </summary>
public class ConfirmPasswordResetRequest
{
    /// <summary>
    /// Reset token
    /// </summary>
    [Required]
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// New password
    /// </summary>
    [Required]
    [MinLength(8)]
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// New password confirmation
    /// </summary>
    [Required]
    [Compare(nameof(NewPassword))]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// API key creation request model
/// </summary>
public class CreateApiKeyRequest
{
    /// <summary>
    /// API key name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API key expiry date (null = never expires)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Rate limit per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Allowed IP addresses
    /// </summary>
    public List<string>? AllowedIpAddresses { get; set; }

    /// <summary>
    /// Permissions for this API key
    /// </summary>
    public List<string>? Permissions { get; set; }
}

/// <summary>
/// API key response model
/// </summary>
public class ApiKeyResponse
{
    /// <summary>
    /// API key ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// API key name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API key value (only returned on creation)
    /// </summary>
    public string? Key { get; set; }

    /// <summary>
    /// API key prefix
    /// </summary>
    public string KeyPrefix { get; set; } = string.Empty;

    /// <summary>
    /// Whether the API key is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// API key expiry date
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Last usage time
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// Usage count
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// Rate limit per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Allowed IP addresses
    /// </summary>
    public List<string>? AllowedIpAddresses { get; set; }

    /// <summary>
    /// Permissions
    /// </summary>
    public List<string>? Permissions { get; set; }

    /// <summary>
    /// Creation date
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
