using System.ComponentModel.DataAnnotations;
using Finbuckle.MultiTenant;

namespace SmsGateway.Core.Models.Authentication;

/// <summary>
/// Role entity for role-based access control
/// </summary>
[MultiTenant]
public class Role
{
    /// <summary>
    /// Unique role identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Role name
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Role display name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Role description
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the role is system-defined (cannot be deleted)
    /// </summary>
    public bool IsSystemRole { get; set; } = false;

    /// <summary>
    /// Whether the role is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Role permissions (JSON array)
    /// </summary>
    public string? Permissions { get; set; }

    /// <summary>
    /// When the role was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the role was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the role
    /// </summary>
    [MaxLength(100)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Navigation property to user roles
    /// </summary>
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
}

/// <summary>
/// User-Role mapping entity
/// </summary>
[MultiTenant]
public class UserRole
{
    /// <summary>
    /// Unique identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// User ID
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// Role ID
    /// </summary>
    [Required]
    public Guid RoleId { get; set; }

    /// <summary>
    /// When the role was assigned
    /// </summary>
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who assigned the role
    /// </summary>
    [MaxLength(100)]
    public string? AssignedBy { get; set; }

    /// <summary>
    /// Navigation property to user
    /// </summary>
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// Navigation property to role
    /// </summary>
    public virtual Role Role { get; set; } = null!;
}

/// <summary>
/// Predefined system permissions
/// </summary>
public static class Permissions
{
    // SMS permissions
    public const string SMS_SEND = "sms:send";
    public const string SMS_VIEW = "sms:view";
    public const string SMS_VIEW_ALL = "sms:view:all";
    public const string SMS_DELETE = "sms:delete";

    // Template permissions
    public const string TEMPLATE_CREATE = "template:create";
    public const string TEMPLATE_UPDATE = "template:update";
    public const string TEMPLATE_DELETE = "template:delete";
    public const string TEMPLATE_VIEW = "template:view";
    public const string TEMPLATE_VIEW_ALL = "template:view:all";

    // Provider permissions
    public const string PROVIDER_VIEW = "provider:view";
    public const string PROVIDER_CONFIGURE = "provider:configure";
    public const string PROVIDER_MANAGE = "provider:manage";

    // User management permissions
    public const string USER_CREATE = "user:create";
    public const string USER_UPDATE = "user:update";
    public const string USER_DELETE = "user:delete";
    public const string USER_VIEW = "user:view";
    public const string USER_VIEW_ALL = "user:view:all";

    // Role management permissions
    public const string ROLE_CREATE = "role:create";
    public const string ROLE_UPDATE = "role:update";
    public const string ROLE_DELETE = "role:delete";
    public const string ROLE_VIEW = "role:view";
    public const string ROLE_ASSIGN = "role:assign";

    // Tenant management permissions
    public const string TENANT_CREATE = "tenant:create";
    public const string TENANT_UPDATE = "tenant:update";
    public const string TENANT_DELETE = "tenant:delete";
    public const string TENANT_VIEW = "tenant:view";

    // System administration permissions
    public const string SYSTEM_ADMIN = "system:admin";
    public const string SYSTEM_LOGS = "system:logs";
    public const string SYSTEM_METRICS = "system:metrics";
    public const string SYSTEM_HEALTH = "system:health";

    /// <summary>
    /// Get all available permissions
    /// </summary>
    public static List<string> GetAllPermissions()
    {
        return typeof(Permissions)
            .GetFields()
            .Where(f => f.IsLiteral && !f.IsInitOnly && f.FieldType == typeof(string))
            .Select(f => f.GetValue(null)?.ToString() ?? "")
            .Where(v => !string.IsNullOrEmpty(v))
            .ToList();
    }
}
