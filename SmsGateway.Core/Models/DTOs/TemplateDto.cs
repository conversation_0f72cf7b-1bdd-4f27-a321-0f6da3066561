namespace SmsGateway.Core.Models.DTOs;

/// <summary>
/// Data Transfer Object for SMS templates
/// </summary>
public class TemplateDto
{
    /// <summary>
    /// Unique template identifier
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Template name/identifier
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template display name
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Template content with Liquid syntax
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Template category
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Template language/locale
    /// </summary>
    public string Language { get; set; } = "en";

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Template version
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Required variables for this template (JSON array)
    /// </summary>
    public string? RequiredVariables { get; set; }

    /// <summary>
    /// Optional variables for this template (JSON array)
    /// </summary>
    public string? OptionalVariables { get; set; }

    /// <summary>
    /// Sample variables for testing (JSON object)
    /// </summary>
    public string? SampleVariables { get; set; }

    /// <summary>
    /// Template tags for organization
    /// </summary>
    public string? Tags { get; set; }

    /// <summary>
    /// Default sender ID for this template
    /// </summary>
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Preferred provider for this template
    /// </summary>
    public string? PreferredProvider { get; set; }

    /// <summary>
    /// Template usage count
    /// </summary>
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// When the template was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the template was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the template
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Who last updated the template
    /// </summary>
    public string? UpdatedBy { get; set; }
}
