using System.ComponentModel.DataAnnotations;
using SmsGateway.Core.Models.Common;

namespace SmsGateway.Core.Models.Requests;

/// <summary>
/// Request model for sending an SMS message
/// </summary>
public class SendSmsRequest
{
    /// <summary>
    /// Recipient phone number (E.164 format recommended)
    /// </summary>
    [Required]
    [Phone]
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender phone number or alphanumeric sender ID
    /// </summary>
    public string? From { get; set; }

    /// <summary>
    /// SMS message content
    /// </summary>
    [Required]
    [StringLength(1600, MinimumLength = 1)]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Optional provider to use for sending (if not specified, uses default)
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Optional callback URL for delivery status updates
    /// </summary>
    [Url]
    public string? CallbackUrl { get; set; }

    /// <summary>
    /// Optional reference ID for tracking
    /// </summary>
    public string? Reference { get; set; }

    /// <summary>
    /// Priority level for the message
    /// </summary>
    public SmsPriority Priority { get; set; } = SmsPriority.Normal;

    /// <summary>
    /// Optional scheduled send time (UTC)
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Template ID to use for message processing
    /// </summary>
    public Guid? TemplateId { get; set; }

    /// <summary>
    /// Template name to use (alternative to TemplateId)
    /// </summary>
    public string? TemplateName { get; set; }

    /// <summary>
    /// Variables for template processing
    /// </summary>
    public Dictionary<string, object>? TemplateVariables { get; set; }

    /// <summary>
    /// Whether to queue the message for background processing
    /// </summary>
    public bool QueueForProcessing { get; set; } = false;

    /// <summary>
    /// Maximum retry attempts for this message
    /// </summary>
    public int? MaxRetries { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Request model for sending bulk SMS messages
/// </summary>
public class SendBulkSmsRequest
{
    /// <summary>
    /// Collection of SMS requests
    /// </summary>
    [Required]
    [MinLength(1)]
    [MaxLength(1000)]
    public List<SendSmsRequest> Messages { get; set; } = new();

    /// <summary>
    /// Default provider for all messages (can be overridden per message)
    /// </summary>
    public string? DefaultProvider { get; set; }

    /// <summary>
    /// Default sender ID for all messages (can be overridden per message)
    /// </summary>
    public string? DefaultFrom { get; set; }

    /// <summary>
    /// Whether to queue all messages for background processing
    /// </summary>
    public bool QueueForProcessing { get; set; } = true;

    /// <summary>
    /// Batch processing priority
    /// </summary>
    public SmsPriority Priority { get; set; } = SmsPriority.Normal;
}

/// <summary>
/// Request model for getting message status
/// </summary>
public class GetMessageStatusRequest
{
    /// <summary>
    /// Message ID returned from send operation
    /// </summary>
    [Required]
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Provider that sent the message
    /// </summary>
    [Required]
    public string Provider { get; set; } = string.Empty;
}

/// <summary>
/// Request model for getting cost estimation
/// </summary>
public class GetCostEstimateRequest
{
    /// <summary>
    /// Recipient phone number
    /// </summary>
    [Required]
    [Phone]
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Message content for cost calculation
    /// </summary>
    [Required]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Optional specific provider for estimation
    /// </summary>
    public string? Provider { get; set; }
}

/// <summary>
/// Request model for SMS history query
/// </summary>
public class GetSmsHistoryRequest
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    [Range(1, int.MaxValue)]
    public int Page { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    [Range(1, 100)]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Filter by status
    /// </summary>
    public SmsStatus? Status { get; set; }

    /// <summary>
    /// Filter by provider
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Filter by phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Filter from date
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// Filter to date
    /// </summary>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// Search in message content
    /// </summary>
    public string? SearchText { get; set; }
}

/// <summary>
/// Request model for sending template-based SMS
/// </summary>
public class SendTemplateSmsRequest
{
    /// <summary>
    /// Recipient phone number
    /// </summary>
    [Required]
    [Phone]
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender ID
    /// </summary>
    public string? From { get; set; }

    /// <summary>
    /// Template ID
    /// </summary>
    [Required]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// Template parameters
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// Language code for template
    /// </summary>
    public string? Language { get; set; }

    /// <summary>
    /// Provider to use
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Priority level
    /// </summary>
    public SmsPriority Priority { get; set; } = SmsPriority.Normal;
}

/// <summary>
/// Request model for sending MMS
/// </summary>
public class SendMmsRequest
{
    /// <summary>
    /// Recipient phone number
    /// </summary>
    [Required]
    [Phone]
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender ID
    /// </summary>
    public string? From { get; set; }

    /// <summary>
    /// Message subject
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// Message text content
    /// </summary>
    public string? Text { get; set; }

    /// <summary>
    /// Media attachments
    /// </summary>
    public List<MmsAttachment> Attachments { get; set; } = new();

    /// <summary>
    /// Provider to use
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Priority level
    /// </summary>
    public SmsPriority Priority { get; set; } = SmsPriority.Normal;
}

/// <summary>
/// MMS attachment model
/// </summary>
public class MmsAttachment
{
    /// <summary>
    /// File name
    /// </summary>
    [Required]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Content type (MIME type)
    /// </summary>
    [Required]
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// File content (base64 encoded)
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long Size { get; set; }
}

/// <summary>
/// Request model for getting inbox messages
/// </summary>
public class GetInboxRequest
{
    /// <summary>
    /// Page number
    /// </summary>
    [Range(1, int.MaxValue)]
    public int Page { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    [Range(1, 100)]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Filter by sender
    /// </summary>
    public string? From { get; set; }

    /// <summary>
    /// Filter from date
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// Filter to date
    /// </summary>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// Search in message content
    /// </summary>
    public string? SearchText { get; set; }

    /// <summary>
    /// Only unread messages
    /// </summary>
    public bool UnreadOnly { get; set; } = false;
}


