using System.ComponentModel.DataAnnotations;

namespace SmsGateway.Core.Models.Requests;

/// <summary>
/// Request model for loading a plugin
/// </summary>
public class LoadPluginRequest
{
    /// <summary>
    /// Path to the plugin assembly file
    /// </summary>
    [Required]
    public string PluginPath { get; set; } = string.Empty;

    /// <summary>
    /// Whether to force reload if already loaded
    /// </summary>
    public bool ForceReload { get; set; } = false;
}

/// <summary>
/// Request model for unloading a plugin
/// </summary>
public class UnloadPluginRequest
{
    /// <summary>
    /// Name of the plugin to unload
    /// </summary>
    [Required]
    public string PluginName { get; set; } = string.Empty;
}

/// <summary>
/// Request model for updating provider configuration
/// </summary>
public class UpdateProviderConfigurationRequest
{
    /// <summary>
    /// Provider name
    /// </summary>
    [Required]
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// Whether the provider is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Provider priority (lower = higher priority)
    /// </summary>
    [Range(1, 1000)]
    public int Priority { get; set; } = 100;

    /// <summary>
    /// Maximum retry attempts
    /// </summary>
    [Range(0, 10)]
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Timeout in seconds
    /// </summary>
    [Range(5, 300)]
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Rate limit per minute
    /// </summary>
    [Range(1, 10000)]
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Default sender ID
    /// </summary>
    [MaxLength(20)]
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Supported countries (ISO codes)
    /// </summary>
    public List<string>? SupportedCountries { get; set; }

    /// <summary>
    /// Provider-specific settings
    /// </summary>
    public Dictionary<string, object>? Settings { get; set; }
}

/// <summary>
/// Request model for getting provider health status
/// </summary>
public class GetProviderHealthRequest
{
    /// <summary>
    /// Specific provider name (optional - if not provided, returns all)
    /// </summary>
    public string? ProviderName { get; set; }

    /// <summary>
    /// Whether to force a fresh health check
    /// </summary>
    public bool ForceRefresh { get; set; } = false;
}

/// <summary>
/// Request model for getting provider statistics
/// </summary>
public class GetProviderStatisticsRequest
{
    /// <summary>
    /// Specific provider name (optional - if not provided, returns all)
    /// </summary>
    public string? ProviderName { get; set; }

    /// <summary>
    /// Start date for statistics
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// End date for statistics
    /// </summary>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// Include detailed breakdown
    /// </summary>
    public bool IncludeDetails { get; set; } = false;
}

/// <summary>
/// Request model for testing provider connectivity
/// </summary>
public class TestProviderRequest
{
    /// <summary>
    /// Provider name to test
    /// </summary>
    [Required]
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// Test phone number (optional - uses default test number if not provided)
    /// </summary>
    [Phone]
    public string? TestPhoneNumber { get; set; }

    /// <summary>
    /// Test message content
    /// </summary>
    public string TestMessage { get; set; } = "Test message from SMS Gateway";

    /// <summary>
    /// Whether to actually send the test message or just validate configuration
    /// </summary>
    public bool SendActualMessage { get; set; } = false;
}
