using System.ComponentModel.DataAnnotations;

namespace SmsGateway.Core.Models.Requests;

/// <summary>
/// Request model for creating SMS templates
/// </summary>
public class CreateTemplateRequest
{
    /// <summary>
    /// Template name/identifier
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template display name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Template content with Liquid syntax
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Template category
    /// </summary>
    [MaxLength(50)]
    public string? Category { get; set; }

    /// <summary>
    /// Template language/locale
    /// </summary>
    [MaxLength(10)]
    public string Language { get; set; } = "en";

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Required variables for this template
    /// </summary>
    public List<string>? RequiredVariables { get; set; }

    /// <summary>
    /// Optional variables for this template
    /// </summary>
    public List<string>? OptionalVariables { get; set; }

    /// <summary>
    /// Sample variables for testing
    /// </summary>
    public Dictionary<string, object>? SampleVariables { get; set; }

    /// <summary>
    /// Template tags for organization
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Default sender ID for this template
    /// </summary>
    [MaxLength(20)]
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Preferred provider for this template
    /// </summary>
    [MaxLength(50)]
    public string? PreferredProvider { get; set; }
}

/// <summary>
/// Request model for updating SMS templates
/// </summary>
public class UpdateTemplateRequest
{
    /// <summary>
    /// Template display name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Template content with Liquid syntax
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Template category
    /// </summary>
    [MaxLength(50)]
    public string? Category { get; set; }

    /// <summary>
    /// Template language/locale
    /// </summary>
    [MaxLength(10)]
    public string Language { get; set; } = "en";

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Required variables for this template
    /// </summary>
    public List<string>? RequiredVariables { get; set; }

    /// <summary>
    /// Optional variables for this template
    /// </summary>
    public List<string>? OptionalVariables { get; set; }

    /// <summary>
    /// Sample variables for testing
    /// </summary>
    public Dictionary<string, object>? SampleVariables { get; set; }

    /// <summary>
    /// Template tags for organization
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Default sender ID for this template
    /// </summary>
    [MaxLength(20)]
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Preferred provider for this template
    /// </summary>
    [MaxLength(50)]
    public string? PreferredProvider { get; set; }
}

/// <summary>
/// Request model for testing template rendering
/// </summary>
public class TestTemplateRequest
{
    /// <summary>
    /// Template content to test
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Variables to use for testing
    /// </summary>
    [Required]
    public Dictionary<string, object> Variables { get; set; } = new();
}

/// <summary>
/// Request model for getting templates
/// </summary>
public class GetTemplatesRequest
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    [Range(1, int.MaxValue)]
    public int Page { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    [Range(1, 100)]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Filter by category
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Search in template name or content
    /// </summary>
    public string? SearchText { get; set; }

    /// <summary>
    /// Filter by language
    /// </summary>
    public string? Language { get; set; }

    /// <summary>
    /// Filter by tags
    /// </summary>
    public List<string>? Tags { get; set; }
}

/// <summary>
/// Request model for processing template
/// </summary>
public class ProcessTemplateRequest
{
    /// <summary>
    /// Template ID to process
    /// </summary>
    public Guid? TemplateId { get; set; }

    /// <summary>
    /// Template name to process (alternative to TemplateId)
    /// </summary>
    public string? TemplateName { get; set; }

    /// <summary>
    /// Template content to process directly (if not using ID or name)
    /// </summary>
    public string? TemplateContent { get; set; }

    /// <summary>
    /// Variables for template processing
    /// </summary>
    [Required]
    public Dictionary<string, object> Variables { get; set; } = new();
}
