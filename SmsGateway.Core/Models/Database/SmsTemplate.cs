using System.ComponentModel.DataAnnotations;
using Finbuckle.MultiTenant;

namespace SmsGateway.Core.Models.Database;

/// <summary>
/// Database entity for SMS templates
/// </summary>
[MultiTenant]
public class SmsTemplate
{
    /// <summary>
    /// Unique template identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Template name/identifier
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template display name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Template content with Liquid syntax
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Template category
    /// </summary>
    [MaxLength(50)]
    public string? Category { get; set; }

    /// <summary>
    /// Template language/locale
    /// </summary>
    [MaxLength(10)]
    public string Language { get; set; } = "en";

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Template version
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Required variables for this template (JSON array)
    /// </summary>
    public string? RequiredVariables { get; set; }

    /// <summary>
    /// Optional variables for this template (JSON array)
    /// </summary>
    public string? OptionalVariables { get; set; }

    /// <summary>
    /// Sample variables for testing (JSON object)
    /// </summary>
    public string? SampleVariables { get; set; }

    /// <summary>
    /// Template tags for organization
    /// </summary>
    [MaxLength(500)]
    public string? Tags { get; set; }

    /// <summary>
    /// Default sender ID for this template
    /// </summary>
    [MaxLength(20)]
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Preferred provider for this template
    /// </summary>
    [MaxLength(50)]
    public string? PreferredProvider { get; set; }

    /// <summary>
    /// Template usage count
    /// </summary>
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// When the template was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the template was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the template
    /// </summary>
    [MaxLength(100)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Who last updated the template
    /// </summary>
    [MaxLength(100)]
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// Navigation property to messages using this template
    /// </summary>
    public virtual ICollection<SmsMessage> Messages { get; set; } = new List<SmsMessage>();
}
