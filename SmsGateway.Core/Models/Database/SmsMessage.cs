using System.ComponentModel.DataAnnotations;
using Finbuckle.MultiTenant;

namespace SmsGateway.Core.Models.Database;

/// <summary>
/// Database entity for SMS messages
/// </summary>
[MultiTenant]
public class SmsMessage
{
    /// <summary>
    /// Unique message identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// External message ID from the provider
    /// </summary>
    [MaxLength(255)]
    public string? ExternalMessageId { get; set; }

    /// <summary>
    /// Recipient phone number
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender phone number or ID
    /// </summary>
    [MaxLength(20)]
    public string? From { get; set; }

    /// <summary>
    /// Original message content (before template processing)
    /// </summary>
    [Required]
    public string OriginalMessage { get; set; } = string.Empty;

    /// <summary>
    /// Final processed message content
    /// </summary>
    [Required]
    public string ProcessedMessage { get; set; } = string.Empty;

    /// <summary>
    /// Template ID used (if any)
    /// </summary>
    public Guid? TemplateId { get; set; }

    /// <summary>
    /// Template variables (JSON)
    /// </summary>
    public string? TemplateVariables { get; set; }

    /// <summary>
    /// Provider used to send the message
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Current message status
    /// </summary>
    public SmsStatus Status { get; set; } = SmsStatus.Pending;

    /// <summary>
    /// Message priority
    /// </summary>
    public SmsPriority Priority { get; set; } = SmsPriority.Normal;

    /// <summary>
    /// Number of message segments
    /// </summary>
    public int Segments { get; set; } = 1;

    /// <summary>
    /// Cost of sending the message
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Currency of the cost
    /// </summary>
    [MaxLength(3)]
    public string? Currency { get; set; }

    /// <summary>
    /// Error message if sending failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if sending failed
    /// </summary>
    [MaxLength(50)]
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Number of retry attempts
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// Maximum retry attempts allowed
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// When the message was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the message was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the message was sent (if successful)
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// When the message was delivered (if confirmed)
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Scheduled send time (if scheduled)
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Callback URL for delivery notifications
    /// </summary>
    public string? CallbackUrl { get; set; }

    /// <summary>
    /// Reference ID for tracking
    /// </summary>
    [MaxLength(255)]
    public string? Reference { get; set; }

    /// <summary>
    /// Additional metadata (JSON)
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// IP address of the request origin
    /// </summary>
    [MaxLength(45)]
    public string? OriginIpAddress { get; set; }

    /// <summary>
    /// User agent of the request
    /// </summary>
    [MaxLength(500)]
    public string? UserAgent { get; set; }

    /// <summary>
    /// Navigation property to template
    /// </summary>
    public virtual SmsTemplate? Template { get; set; }

    /// <summary>
    /// Navigation property to status history
    /// </summary>
    public virtual ICollection<SmsStatusHistory> StatusHistory { get; set; } = new List<SmsStatusHistory>();
}
