using System.ComponentModel.DataAnnotations;
using Finbuckle.MultiTenant;

namespace SmsGateway.Core.Models.Database;

/// <summary>
/// Database entity for SMS queue management
/// </summary>
[MultiTenant]
public class SmsQueue
{
    /// <summary>
    /// Unique queue item identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// SMS message ID
    /// </summary>
    [Required]
    public Guid SmsMessageId { get; set; }

    /// <summary>
    /// Queue status
    /// </summary>
    public QueueStatus Status { get; set; } = QueueStatus.Pending;

    /// <summary>
    /// Queue priority (lower number = higher priority)
    /// </summary>
    public int Priority { get; set; } = 100;

    /// <summary>
    /// Number of processing attempts
    /// </summary>
    public int AttemptCount { get; set; } = 0;

    /// <summary>
    /// Maximum number of attempts allowed
    /// </summary>
    public int MaxAttempts { get; set; } = 3;

    /// <summary>
    /// When to process this item (for scheduling)
    /// </summary>
    public DateTime ProcessAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the item was added to queue
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the item was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When processing started (if in progress)
    /// </summary>
    public DateTime? ProcessingStartedAt { get; set; }

    /// <summary>
    /// When processing completed
    /// </summary>
    public DateTime? ProcessedAt { get; set; }

    /// <summary>
    /// Last error message
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// Hangfire job ID
    /// </summary>
    [MaxLength(100)]
    public string? HangfireJobId { get; set; }

    /// <summary>
    /// Additional processing metadata (JSON)
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Navigation property to SMS message
    /// </summary>
    public virtual SmsMessage SmsMessage { get; set; } = null!;
}

/// <summary>
/// Queue processing status
/// </summary>
public enum QueueStatus
{
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4,
    Scheduled = 5
}
