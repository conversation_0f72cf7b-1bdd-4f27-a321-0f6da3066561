using System.ComponentModel.DataAnnotations;
using Finbuckle.MultiTenant;

namespace SmsGateway.Core.Models.Database;

/// <summary>
/// Database entity for SMS status history tracking
/// </summary>
[MultiTenant]
public class SmsStatusHistory
{
    /// <summary>
    /// Unique status history identifier
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [Required]
    [MaxLength(64)]
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// SMS message ID
    /// </summary>
    [Required]
    public Guid SmsMessageId { get; set; }

    /// <summary>
    /// Previous status
    /// </summary>
    public SmsStatus? PreviousStatus { get; set; }

    /// <summary>
    /// New status
    /// </summary>
    public SmsStatus NewStatus { get; set; }

    /// <summary>
    /// Status change reason/description
    /// </summary>
    [MaxLength(500)]
    public string? Reason { get; set; }

    /// <summary>
    /// Provider response data (JSON)
    /// </summary>
    public string? ProviderResponse { get; set; }

    /// <summary>
    /// Error message if status change was due to error
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if status change was due to error
    /// </summary>
    [MaxLength(50)]
    public string? ErrorCode { get; set; }

    /// <summary>
    /// When the status change occurred
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Additional metadata (JSON)
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Navigation property to SMS message
    /// </summary>
    public virtual SmsMessage SmsMessage { get; set; } = null!;
}
