namespace SmsGateway.Core.Models.Responses;

/// <summary>
/// Response model for provider information
/// </summary>
public class ProviderInfoResponse
{
    /// <summary>
    /// Provider name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Provider display name
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Provider version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Provider description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Whether the provider is enabled
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Whether the provider is available
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Provider priority
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// Maximum retry attempts
    /// </summary>
    public int MaxRetries { get; set; }

    /// <summary>
    /// Timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; }

    /// <summary>
    /// Rate limit per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Default sender ID
    /// </summary>
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Supported countries
    /// </summary>
    public List<string> SupportedCountries { get; set; } = new();

    /// <summary>
    /// Configuration type
    /// </summary>
    public string ConfigurationType { get; set; } = string.Empty;

    /// <summary>
    /// Last health check time
    /// </summary>
    public DateTime? LastHealthCheck { get; set; }

    /// <summary>
    /// Whether the last health check was successful
    /// </summary>
    public bool? IsHealthy { get; set; }
}

/// <summary>
/// Response model for provider health status
/// </summary>
public class ProviderHealthResponse
{
    /// <summary>
    /// Provider name
    /// </summary>
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// Whether the provider is healthy
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// Health status message
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Response time for health check
    /// </summary>
    public TimeSpan ResponseTime { get; set; }

    /// <summary>
    /// When the health check was performed
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Additional health data
    /// </summary>
    public Dictionary<string, object>? Data { get; set; }

    /// <summary>
    /// Error details if unhealthy
    /// </summary>
    public string? ErrorDetails { get; set; }
}

/// <summary>
/// Response model for provider statistics
/// </summary>
public class ProviderStatisticsResponse
{
    /// <summary>
    /// Provider name
    /// </summary>
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// Total messages sent
    /// </summary>
    public int TotalMessagesSent { get; set; }

    /// <summary>
    /// Successful messages
    /// </summary>
    public int SuccessfulMessages { get; set; }

    /// <summary>
    /// Failed messages
    /// </summary>
    public int FailedMessages { get; set; }

    /// <summary>
    /// Success rate percentage
    /// </summary>
    public decimal SuccessRate { get; set; }

    /// <summary>
    /// Average response time
    /// </summary>
    public TimeSpan AverageResponseTime { get; set; }

    /// <summary>
    /// Last time the provider was used
    /// </summary>
    public DateTime? LastUsed { get; set; }

    /// <summary>
    /// Whether the provider is currently healthy
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// Last error message
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// Total cost (if available)
    /// </summary>
    public decimal? TotalCost { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// Statistics period start
    /// </summary>
    public DateTime? PeriodStart { get; set; }

    /// <summary>
    /// Statistics period end
    /// </summary>
    public DateTime? PeriodEnd { get; set; }

    /// <summary>
    /// Detailed breakdown by day/hour
    /// </summary>
    public Dictionary<string, int>? DetailedBreakdown { get; set; }
}

/// <summary>
/// Response model for plugin information
/// </summary>
public class PluginInfoResponse
{
    /// <summary>
    /// Plugin name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Plugin version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Plugin author
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// Plugin description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Whether the plugin is loaded
    /// </summary>
    public bool IsLoaded { get; set; }

    /// <summary>
    /// When the plugin was loaded
    /// </summary>
    public DateTime? LoadedAt { get; set; }

    /// <summary>
    /// Plugin assembly path
    /// </summary>
    public string? AssemblyPath { get; set; }

    /// <summary>
    /// Plugin file size
    /// </summary>
    public long? FileSize { get; set; }

    /// <summary>
    /// Plugin file hash
    /// </summary>
    public string? FileHash { get; set; }

    /// <summary>
    /// Provider names provided by this plugin
    /// </summary>
    public List<string> ProviderNames { get; set; } = new();

    /// <summary>
    /// Plugin tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Minimum core version required
    /// </summary>
    public string? MinimumCoreVersion { get; set; }

    /// <summary>
    /// Plugin dependencies
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// Load errors (if any)
    /// </summary>
    public List<string> LoadErrors { get; set; } = new();
}

/// <summary>
/// Response model for plugin loading operations
/// </summary>
public class PluginLoadResponse
{
    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Plugin name
    /// </summary>
    public string? PluginName { get; set; }

    /// <summary>
    /// Plugin metadata
    /// </summary>
    public PluginInfoResponse? Metadata { get; set; }

    /// <summary>
    /// Providers loaded from this plugin
    /// </summary>
    public List<string> LoadedProviders { get; set; } = new();

    /// <summary>
    /// Error messages
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Warning messages
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Operation timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Response model for provider testing
/// </summary>
public class ProviderTestResponse
{
    /// <summary>
    /// Whether the test was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Provider name that was tested
    /// </summary>
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// Test message
    /// </summary>
    public string? TestMessage { get; set; }

    /// <summary>
    /// Test phone number used
    /// </summary>
    public string? TestPhoneNumber { get; set; }

    /// <summary>
    /// Whether an actual message was sent
    /// </summary>
    public bool ActualMessageSent { get; set; }

    /// <summary>
    /// Message ID if message was sent
    /// </summary>
    public string? MessageId { get; set; }

    /// <summary>
    /// Test response time
    /// </summary>
    public TimeSpan ResponseTime { get; set; }

    /// <summary>
    /// Error message if test failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Additional test details
    /// </summary>
    public Dictionary<string, object>? TestDetails { get; set; }

    /// <summary>
    /// Test timestamp
    /// </summary>
    public DateTime TestedAt { get; set; } = DateTime.UtcNow;
}
