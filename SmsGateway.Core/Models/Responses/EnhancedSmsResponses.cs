using SmsGateway.Core.Models.Common;

namespace SmsGateway.Core.Models.Responses;

/// <summary>
/// Response model for message details
/// </summary>
public class MessageDetailsResponse
{
    /// <summary>
    /// Message ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// External message ID from provider
    /// </summary>
    public string? ExternalMessageId { get; set; }

    /// <summary>
    /// Recipient phone number
    /// </summary>
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender ID
    /// </summary>
    public string? From { get; set; }

    /// <summary>
    /// Message content
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Message type
    /// </summary>
    public MessageType MessageType { get; set; }

    /// <summary>
    /// Current status
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Provider used
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Number of segments
    /// </summary>
    public int Segments { get; set; }

    /// <summary>
    /// Cost
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// When created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// When delivered
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Response model for delivery reports
/// </summary>
public class DeliveryReportResponse
{
    /// <summary>
    /// Message ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Delivery status
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Delivery timestamp
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Error code if failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Error description
    /// </summary>
    public string? ErrorDescription { get; set; }

    /// <summary>
    /// Network operator
    /// </summary>
    public string? NetworkOperator { get; set; }

    /// <summary>
    /// Country code
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// Delivery attempts
    /// </summary>
    public int DeliveryAttempts { get; set; }

    /// <summary>
    /// Final delivery status
    /// </summary>
    public bool IsFinalStatus { get; set; }
}

/// <summary>
/// Response model for pricing information
/// </summary>
public class PricingInfoResponse
{
    /// <summary>
    /// Country code
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// Country name
    /// </summary>
    public string? CountryName { get; set; }

    /// <summary>
    /// Price per SMS
    /// </summary>
    public decimal PricePerSms { get; set; }

    /// <summary>
    /// Price per MMS
    /// </summary>
    public decimal? PricePerMms { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Network operators and their prices
    /// </summary>
    public List<OperatorPricing>? OperatorPricing { get; set; }

    /// <summary>
    /// Effective date
    /// </summary>
    public DateTime EffectiveDate { get; set; }
}

/// <summary>
/// Operator pricing information
/// </summary>
public class OperatorPricing
{
    /// <summary>
    /// Operator name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Operator code
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Price per SMS
    /// </summary>
    public decimal PricePerSms { get; set; }

    /// <summary>
    /// Price per MMS
    /// </summary>
    public decimal? PricePerMms { get; set; }
}

/// <summary>
/// Response model for account balance
/// </summary>
public class AccountBalanceResponse
{
    /// <summary>
    /// Current balance
    /// </summary>
    public decimal Balance { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Credit balance (if applicable)
    /// </summary>
    public decimal? CreditBalance { get; set; }

    /// <summary>
    /// Low balance threshold
    /// </summary>
    public decimal? LowBalanceThreshold { get; set; }

    /// <summary>
    /// Whether balance is low
    /// </summary>
    public bool IsLowBalance { get; set; }

    /// <summary>
    /// Last updated
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Auto-recharge settings
    /// </summary>
    public AutoRechargeSettings? AutoRecharge { get; set; }
}

/// <summary>
/// Auto-recharge settings
/// </summary>
public class AutoRechargeSettings
{
    /// <summary>
    /// Whether auto-recharge is enabled
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// Trigger amount
    /// </summary>
    public decimal TriggerAmount { get; set; }

    /// <summary>
    /// Recharge amount
    /// </summary>
    public decimal RechargeAmount { get; set; }
}

/// <summary>
/// Response model for phone validation
/// </summary>
public class PhoneValidationResponse
{
    /// <summary>
    /// Whether the phone number is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Formatted phone number
    /// </summary>
    public string? FormattedNumber { get; set; }

    /// <summary>
    /// Country code
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// Country name
    /// </summary>
    public string? CountryName { get; set; }

    /// <summary>
    /// Network operator
    /// </summary>
    public string? NetworkOperator { get; set; }

    /// <summary>
    /// Number type (mobile, landline, etc.)
    /// </summary>
    public string? NumberType { get; set; }

    /// <summary>
    /// Validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Response model for message validation
/// </summary>
public class MessageValidationResponse
{
    /// <summary>
    /// Whether the message is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Character count
    /// </summary>
    public int CharacterCount { get; set; }

    /// <summary>
    /// Number of SMS segments
    /// </summary>
    public int SegmentCount { get; set; }

    /// <summary>
    /// Whether message contains Unicode characters
    /// </summary>
    public bool ContainsUnicode { get; set; }

    /// <summary>
    /// Detected encoding
    /// </summary>
    public string Encoding { get; set; } = string.Empty;

    /// <summary>
    /// Validation warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Response model for rate limit status
/// </summary>
public class RateLimitResponse
{
    /// <summary>
    /// Current rate limit per minute
    /// </summary>
    public int LimitPerMinute { get; set; }

    /// <summary>
    /// Remaining requests in current window
    /// </summary>
    public int Remaining { get; set; }

    /// <summary>
    /// When the rate limit window resets
    /// </summary>
    public DateTime ResetTime { get; set; }

    /// <summary>
    /// Whether rate limit is exceeded
    /// </summary>
    public bool IsExceeded { get; set; }

    /// <summary>
    /// Retry after seconds if exceeded
    /// </summary>
    public int? RetryAfterSeconds { get; set; }
}

/// <summary>
/// Response model for inbox messages
/// </summary>
public class InboxResponse
{
    /// <summary>
    /// Inbox messages
    /// </summary>
    public List<InboxMessage> Messages { get; set; } = new();

    /// <summary>
    /// Total count
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Page number
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Whether there are more pages
    /// </summary>
    public bool HasMore { get; set; }
}

/// <summary>
/// Inbox message model
/// </summary>
public class InboxMessage
{
    /// <summary>
    /// Message ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Sender phone number
    /// </summary>
    public string From { get; set; } = string.Empty;

    /// <summary>
    /// Recipient phone number
    /// </summary>
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Message content
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// When received
    /// </summary>
    public DateTime ReceivedAt { get; set; }

    /// <summary>
    /// Whether message is read
    /// </summary>
    public bool IsRead { get; set; }

    /// <summary>
    /// Message type
    /// </summary>
    public MessageType MessageType { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Response model for webhook configuration
/// </summary>
public class WebhookConfigResponse
{
    /// <summary>
    /// Whether configuration was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Webhook URL
    /// </summary>
    public string WebhookUrl { get; set; } = string.Empty;

    /// <summary>
    /// Configured events
    /// </summary>
    public List<WebhookEvent> Events { get; set; } = new();

    /// <summary>
    /// Webhook secret for verification
    /// </summary>
    public string? Secret { get; set; }

    /// <summary>
    /// Error message if configuration failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}
