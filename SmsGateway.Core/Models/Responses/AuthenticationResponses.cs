namespace SmsGateway.Core.Models.Responses;

/// <summary>
/// Login response model
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// Whether login was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// JWT access token
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// Refresh token
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// Token expiry time
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// User information
    /// </summary>
    public UserInfoResponse? User { get; set; }

    /// <summary>
    /// Error message if login failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether account is locked
    /// </summary>
    public bool IsLocked { get; set; }

    /// <summary>
    /// Lock expiry time if account is locked
    /// </summary>
    public DateTime? LockedUntil { get; set; }

    /// <summary>
    /// Login timestamp
    /// </summary>
    public DateTime LoginAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// User information response model
/// </summary>
public class UserInfoResponse
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// First name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Whether email is verified
    /// </summary>
    public bool EmailVerified { get; set; }

    /// <summary>
    /// User roles
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// User permissions
    /// </summary>
    public List<string> Permissions { get; set; } = new();

    /// <summary>
    /// Tenant ID
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// User timezone
    /// </summary>
    public string TimeZone { get; set; } = "UTC";

    /// <summary>
    /// User language
    /// </summary>
    public string Language { get; set; } = "en";

    /// <summary>
    /// Last login time
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// Account creation time
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// API key response model
/// </summary>
public class ApiKeyResponse
{
    /// <summary>
    /// API key ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// API key name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API key value (only returned on creation)
    /// </summary>
    public string? Key { get; set; }

    /// <summary>
    /// API key prefix
    /// </summary>
    public string KeyPrefix { get; set; } = string.Empty;

    /// <summary>
    /// Whether the API key is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// API key expiry date
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Last usage time
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// Usage count
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// Rate limit per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Allowed IP addresses
    /// </summary>
    public List<string>? AllowedIpAddresses { get; set; }

    /// <summary>
    /// Permissions
    /// </summary>
    public List<string>? Permissions { get; set; }

    /// <summary>
    /// Creation date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Whether the key is expired
    /// </summary>
    public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow;
}

/// <summary>
/// Token validation result response
/// </summary>
public class TokenValidationResponse
{
    /// <summary>
    /// Whether the token is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// User ID from token
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// Tenant ID from token
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// Token ID (jti claim)
    /// </summary>
    public string? TokenId { get; set; }

    /// <summary>
    /// User permissions
    /// </summary>
    public List<string> Permissions { get; set; } = new();

    /// <summary>
    /// Error message if validation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Token expiry time
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Validation timestamp
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// API key validation result response
/// </summary>
public class ApiKeyValidationResponse
{
    /// <summary>
    /// Whether the API key is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// User ID associated with the API key
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// API key ID
    /// </summary>
    public Guid? ApiKeyId { get; set; }

    /// <summary>
    /// Tenant ID
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// API key permissions
    /// </summary>
    public List<string> Permissions { get; set; } = new();

    /// <summary>
    /// Error message if validation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Rate limit per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Whether rate limit is exceeded
    /// </summary>
    public bool IsRateLimited { get; set; }

    /// <summary>
    /// Validation timestamp
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Registration response model
/// </summary>
public class RegisterResponse
{
    /// <summary>
    /// Whether registration was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Created user information
    /// </summary>
    public UserInfoResponse? User { get; set; }

    /// <summary>
    /// Error message if registration failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether email verification is required
    /// </summary>
    public bool RequiresEmailVerification { get; set; }

    /// <summary>
    /// Registration timestamp
    /// </summary>
    public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Password reset response model
/// </summary>
public class PasswordResetResponse
{
    /// <summary>
    /// Whether the reset request was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Message to display to user
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Error message if request failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Request timestamp
    /// </summary>
    public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
}
