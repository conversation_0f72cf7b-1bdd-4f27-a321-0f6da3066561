namespace SmsGateway.Core.Models.Responses;

/// <summary>
/// Response model for template operations
/// </summary>
public class TemplateResponse
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Template name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template display name
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Template content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Template category
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Template language
    /// </summary>
    public string Language { get; set; } = string.Empty;

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Template version
    /// </summary>
    public int Version { get; set; }

    /// <summary>
    /// Required variables
    /// </summary>
    public List<string>? RequiredVariables { get; set; }

    /// <summary>
    /// Optional variables
    /// </summary>
    public List<string>? OptionalVariables { get; set; }

    /// <summary>
    /// Sample variables
    /// </summary>
    public Dictionary<string, object>? SampleVariables { get; set; }

    /// <summary>
    /// Template tags
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Default sender ID
    /// </summary>
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Preferred provider
    /// </summary>
    public string? PreferredProvider { get; set; }

    /// <summary>
    /// Template usage count
    /// </summary>
    public int UsageCount { get; set; }

    /// <summary>
    /// When the template was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the template was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Who created the template
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Who last updated the template
    /// </summary>
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Response model for template testing
/// </summary>
public class TemplateTestResponse
{
    /// <summary>
    /// Whether the template rendered successfully
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Rendered content
    /// </summary>
    public string? RenderedContent { get; set; }

    /// <summary>
    /// Error message if rendering failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Variables that were used
    /// </summary>
    public Dictionary<string, object>? UsedVariables { get; set; }

    /// <summary>
    /// Variables that were missing
    /// </summary>
    public List<string>? MissingVariables { get; set; }

    /// <summary>
    /// Estimated message segments
    /// </summary>
    public int EstimatedSegments { get; set; }

    /// <summary>
    /// Character count
    /// </summary>
    public int CharacterCount { get; set; }

    /// <summary>
    /// Whether the message contains Unicode characters
    /// </summary>
    public bool ContainsUnicode { get; set; }
}

/// <summary>
/// Response model for template list queries
/// </summary>
public class TemplateListResponse
{
    /// <summary>
    /// Templates
    /// </summary>
    public List<TemplateResponse> Templates { get; set; } = new();

    /// <summary>
    /// Total count of templates (for pagination)
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// Whether there are more pages
    /// </summary>
    public bool HasNextPage => Page < TotalPages;

    /// <summary>
    /// Whether there are previous pages
    /// </summary>
    public bool HasPreviousPage => Page > 1;
}

/// <summary>
/// Response model for template usage statistics
/// </summary>
public class TemplateUsageStatisticsResponse
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid TemplateId { get; set; }

    /// <summary>
    /// Template name
    /// </summary>
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// Total usage count
    /// </summary>
    public int TotalUsage { get; set; }

    /// <summary>
    /// Successful messages
    /// </summary>
    public int SuccessfulMessages { get; set; }

    /// <summary>
    /// Failed messages
    /// </summary>
    public int FailedMessages { get; set; }

    /// <summary>
    /// Success rate percentage
    /// </summary>
    public decimal SuccessRate => TotalUsage > 0 ? (decimal)SuccessfulMessages / TotalUsage * 100 : 0;

    /// <summary>
    /// Last time the template was used
    /// </summary>
    public DateTime? LastUsed { get; set; }

    /// <summary>
    /// Usage breakdown by provider
    /// </summary>
    public Dictionary<string, int>? UsageByProvider { get; set; }

    /// <summary>
    /// Usage breakdown by month
    /// </summary>
    public Dictionary<string, int>? UsageByMonth { get; set; }

    /// <summary>
    /// Average message segments
    /// </summary>
    public decimal AverageSegments { get; set; }

    /// <summary>
    /// Total cost (if available)
    /// </summary>
    public decimal? TotalCost { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string? Currency { get; set; }
}

/// <summary>
/// Response model for template processing
/// </summary>
public class ProcessTemplateResponse
{
    /// <summary>
    /// Whether processing was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Processed content
    /// </summary>
    public string? ProcessedContent { get; set; }

    /// <summary>
    /// Error message if processing failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Template ID used (if any)
    /// </summary>
    public Guid? TemplateId { get; set; }

    /// <summary>
    /// Variables that were processed
    /// </summary>
    public Dictionary<string, object>? ProcessedVariables { get; set; }

    /// <summary>
    /// Processing timestamp
    /// </summary>
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
}
