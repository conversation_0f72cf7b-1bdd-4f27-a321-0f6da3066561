using SmsGateway.Core.Models.Common;

namespace SmsGateway.Core.Models.Responses;

/// <summary>
/// Response model for SMS operations
/// </summary>
public class SmsResponse
{
    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Message ID assigned by the provider
    /// </summary>
    public string? MessageId { get; set; }

    /// <summary>
    /// Current message status
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Cost of sending the message
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Currency of the cost
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// Number of message segments
    /// </summary>
    public int Segments { get; set; } = 1;

    /// <summary>
    /// Provider used to send the message
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Error message if operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if operation failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Response timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Response model for bulk SMS operations
/// </summary>
public class BulkSmsResponse
{
    /// <summary>
    /// Whether the overall operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Total number of messages processed
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// Number of successfully sent messages
    /// </summary>
    public int SuccessfulMessages { get; set; }

    /// <summary>
    /// Number of failed messages
    /// </summary>
    public int FailedMessages { get; set; }

    /// <summary>
    /// Individual message responses
    /// </summary>
    public List<SmsResponse> MessageResponses { get; set; } = new();

    /// <summary>
    /// Total cost for all messages
    /// </summary>
    public decimal? TotalCost { get; set; }

    /// <summary>
    /// Currency of the cost
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// Response timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Success rate (0-100)
    /// </summary>
    public decimal SuccessRate => TotalMessages > 0 ? (decimal)SuccessfulMessages / TotalMessages * 100 : 0;
}

/// <summary>
/// Response model for message status queries
/// </summary>
public class MessageStatusResponse
{
    /// <summary>
    /// Message ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Current message status
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Status description
    /// </summary>
    public string? StatusDescription { get; set; }

    /// <summary>
    /// Provider that handled the message
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// When the message was sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// When the message was delivered
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Last status update time
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Response model for cost estimation
/// </summary>
public class CostEstimateResponse
{
    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal Cost { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Provider used for estimation
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Number of message segments
    /// </summary>
    public int Segments { get; set; }

    /// <summary>
    /// Cost per segment
    /// </summary>
    public decimal CostPerSegment { get; set; }

    /// <summary>
    /// Country for pricing
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// Estimation timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Response model for SMS history queries
/// </summary>
public class SmsHistoryResponse
{
    /// <summary>
    /// SMS messages
    /// </summary>
    public List<SmsHistoryItem> Messages { get; set; } = new();

    /// <summary>
    /// Total count
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Page number
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Whether there are more pages
    /// </summary>
    public bool HasMore { get; set; }
}

/// <summary>
/// SMS history item
/// </summary>
public class SmsHistoryItem
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// External message ID
    /// </summary>
    public string? ExternalMessageId { get; set; }

    /// <summary>
    /// Recipient phone number
    /// </summary>
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender ID
    /// </summary>
    public string? From { get; set; }

    /// <summary>
    /// Message content
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Current status
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Provider used
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Cost
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// When created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// When delivered
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Template ID used (if any)
    /// </summary>
    public Guid? TemplateId { get; set; }
}
