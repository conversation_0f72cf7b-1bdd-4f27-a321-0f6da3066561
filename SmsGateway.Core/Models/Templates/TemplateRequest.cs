using System.ComponentModel.DataAnnotations;

namespace SmsGateway.Core.Models.Templates;

/// <summary>
/// Request model for creating/updating SMS templates
/// </summary>
public class TemplateRequest
{
    /// <summary>
    /// Template name/identifier
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template display name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Template content with Li<PERSON> syntax
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Template category
    /// </summary>
    [MaxLength(50)]
    public string? Category { get; set; }

    /// <summary>
    /// Template language/locale
    /// </summary>
    [MaxLength(10)]
    public string Language { get; set; } = "en";

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Required variables for this template
    /// </summary>
    public List<string>? RequiredVariables { get; set; }

    /// <summary>
    /// Optional variables for this template
    /// </summary>
    public List<string>? OptionalVariables { get; set; }

    /// <summary>
    /// Sample variables for testing
    /// </summary>
    public Dictionary<string, object>? SampleVariables { get; set; }

    /// <summary>
    /// Template tags for organization
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Default sender ID for this template
    /// </summary>
    [MaxLength(20)]
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Preferred provider for this template
    /// </summary>
    [MaxLength(50)]
    public string? PreferredProvider { get; set; }
}

/// <summary>
/// Response model for template operations
/// </summary>
public class TemplateResponse
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Template name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template display name
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Template content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Template category
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Template language
    /// </summary>
    public string Language { get; set; } = string.Empty;

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Template version
    /// </summary>
    public int Version { get; set; }

    /// <summary>
    /// Required variables
    /// </summary>
    public List<string>? RequiredVariables { get; set; }

    /// <summary>
    /// Optional variables
    /// </summary>
    public List<string>? OptionalVariables { get; set; }

    /// <summary>
    /// Sample variables
    /// </summary>
    public Dictionary<string, object>? SampleVariables { get; set; }

    /// <summary>
    /// Template tags
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Default sender ID
    /// </summary>
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Preferred provider
    /// </summary>
    public string? PreferredProvider { get; set; }

    /// <summary>
    /// Template usage count
    /// </summary>
    public int UsageCount { get; set; }

    /// <summary>
    /// When the template was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the template was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Who created the template
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Who last updated the template
    /// </summary>
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Request model for testing template rendering
/// </summary>
public class TemplateTestRequest
{
    /// <summary>
    /// Template content to test
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Variables to use for testing
    /// </summary>
    [Required]
    public Dictionary<string, object> Variables { get; set; } = new();
}

/// <summary>
/// Response model for template testing
/// </summary>
public class TemplateTestResponse
{
    /// <summary>
    /// Whether the template rendered successfully
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Rendered content
    /// </summary>
    public string? RenderedContent { get; set; }

    /// <summary>
    /// Error message if rendering failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Variables that were used
    /// </summary>
    public Dictionary<string, object>? UsedVariables { get; set; }

    /// <summary>
    /// Variables that were missing
    /// </summary>
    public List<string>? MissingVariables { get; set; }
}
