using System.ComponentModel.DataAnnotations;

namespace SmsGateway.Core.Models.Common;

/// <summary>
/// Base provider configuration
/// </summary>
public class ProviderConfiguration
{
    /// <summary>
    /// Provider name
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Provider display name
    /// </summary>
    [Required]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether the provider is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Provider priority (lower number = higher priority)
    /// </summary>
    public int Priority { get; set; } = 100;

    /// <summary>
    /// Maximum retry attempts
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Rate limit per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Base URL for the provider API
    /// </summary>
    public string? BaseUrl { get; set; }

    /// <summary>
    /// Default sender ID
    /// </summary>
    public string? DefaultSenderId { get; set; }

    /// <summary>
    /// Supported country codes (ISO 3166-1 alpha-2)
    /// </summary>
    public List<string> SupportedCountries { get; set; } = new();

    /// <summary>
    /// Provider-specific settings
    /// </summary>
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// API key based provider configuration
/// </summary>
public class ApiKeyProviderConfiguration : ProviderConfiguration
{
    /// <summary>
    /// API key for authentication
    /// </summary>
    [Required]
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// API secret (if required)
    /// </summary>
    public string? ApiSecret { get; set; }

    /// <summary>
    /// API key header name (default: "X-API-Key")
    /// </summary>
    public string ApiKeyHeader { get; set; } = "X-API-Key";
}

/// <summary>
/// Username/password based provider configuration
/// </summary>
public class UsernamePasswordProviderConfiguration : ProviderConfiguration
{
    /// <summary>
    /// Username for authentication
    /// </summary>
    [Required]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password for authentication
    /// </summary>
    [Required]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Authentication method (Basic, Digest, etc.)
    /// </summary>
    public string AuthenticationMethod { get; set; } = "Basic";
}

/// <summary>
/// OAuth 2.0 based provider configuration
/// </summary>
public class OAuthProviderConfiguration : ProviderConfiguration
{
    /// <summary>
    /// OAuth client ID
    /// </summary>
    [Required]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// OAuth client secret
    /// </summary>
    [Required]
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// OAuth token endpoint
    /// </summary>
    [Required]
    public string TokenEndpoint { get; set; } = string.Empty;

    /// <summary>
    /// OAuth scopes
    /// </summary>
    public List<string> Scopes { get; set; } = new();

    /// <summary>
    /// Current access token
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// Access token expiry time
    /// </summary>
    public DateTime? TokenExpiresAt { get; set; }

    /// <summary>
    /// Refresh token
    /// </summary>
    public string? RefreshToken { get; set; }
}

/// <summary>
/// Validation result for configurations
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// Whether the validation passed
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// Validation error messages
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Validation warning messages
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Health check result model
/// </summary>
public class HealthCheckResult
{
    /// <summary>
    /// Whether the health check passed
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// Health check message
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Response time for the health check
    /// </summary>
    public TimeSpan ResponseTime { get; set; }

    /// <summary>
    /// Additional health data
    /// </summary>
    public Dictionary<string, object>? Data { get; set; }

    /// <summary>
    /// Exception details if health check failed
    /// </summary>
    public string? Exception { get; set; }

    /// <summary>
    /// When the health check was performed
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
}



/// <summary>
/// Provider statistics
/// </summary>
public class ProviderStatistics
{
    /// <summary>
    /// Provider name
    /// </summary>
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// Total messages sent
    /// </summary>
    public int TotalMessagesSent { get; set; }

    /// <summary>
    /// Successful messages
    /// </summary>
    public int SuccessfulMessages { get; set; }

    /// <summary>
    /// Failed messages
    /// </summary>
    public int FailedMessages { get; set; }

    /// <summary>
    /// Success rate (0-100)
    /// </summary>
    public decimal SuccessRate => TotalMessagesSent > 0 ? (decimal)SuccessfulMessages / TotalMessagesSent * 100 : 0;

    /// <summary>
    /// Average response time
    /// </summary>
    public TimeSpan AverageResponseTime { get; set; }

    /// <summary>
    /// Last time the provider was used
    /// </summary>
    public DateTime? LastUsed { get; set; }

    /// <summary>
    /// Whether the provider is currently healthy
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// Last error message
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// Total cost (if available)
    /// </summary>
    public decimal? TotalCost { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string? Currency { get; set; }
}

/// <summary>
/// Plugin metadata
/// </summary>
public class PluginMetadata
{
    /// <summary>
    /// Assembly name
    /// </summary>
    public string AssemblyName { get; set; } = string.Empty;

    /// <summary>
    /// Plugin version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Plugin author
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// Plugin description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Plugin tags
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Minimum core version required
    /// </summary>
    public string MinimumCoreVersion { get; set; } = string.Empty;

    /// <summary>
    /// Plugin dependencies
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// Assembly file path
    /// </summary>
    public string? AssemblyPath { get; set; }

    /// <summary>
    /// Assembly file size
    /// </summary>
    public long? FileSize { get; set; }

    /// <summary>
    /// Assembly file hash
    /// </summary>
    public string? FileHash { get; set; }

    /// <summary>
    /// When the plugin was last modified
    /// </summary>
    public DateTime? LastModified { get; set; }

    /// <summary>
    /// When the plugin was loaded
    /// </summary>
    public DateTime? LoadedAt { get; set; }

    /// <summary>
    /// Whether the plugin is currently loaded
    /// </summary>
    public bool IsLoaded { get; set; }
}
