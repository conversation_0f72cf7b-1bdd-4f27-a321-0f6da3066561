namespace SmsGateway.Core.Models.Common;

/// <summary>
/// Provider feature enumeration
/// </summary>
public enum ProviderFeature
{
    /// <summary>
    /// Basic SMS sending
    /// </summary>
    BasicSms = 0,

    /// <summary>
    /// Bulk SMS sending
    /// </summary>
    BulkSms = 1,

    /// <summary>
    /// Scheduled SMS
    /// </summary>
    ScheduledSms = 2,

    /// <summary>
    /// Delivery reports
    /// </summary>
    DeliveryReports = 3,

    /// <summary>
    /// Two-way SMS (receiving)
    /// </summary>
    TwoWaySms = 4,

    /// <summary>
    /// MMS support
    /// </summary>
    Mms = 5,

    /// <summary>
    /// Template messages
    /// </summary>
    Templates = 6,

    /// <summary>
    /// Unicode support
    /// </summary>
    Unicode = 7,

    /// <summary>
    /// Long messages (concatenated SMS)
    /// </summary>
    LongMessages = 8,

    /// <summary>
    /// Flash SMS
    /// </summary>
    FlashSms = 9,

    /// <summary>
    /// Binary SMS
    /// </summary>
    BinarySms = 10,

    /// <summary>
    /// Alphanumeric sender ID
    /// </summary>
    AlphanumericSenderId = 11,

    /// <summary>
    /// Custom sender ID
    /// </summary>
    CustomSenderId = 12,

    /// <summary>
    /// Webhooks for delivery notifications
    /// </summary>
    Webhooks = 13,

    /// <summary>
    /// Real-time status tracking
    /// </summary>
    RealTimeStatus = 14,

    /// <summary>
    /// Message expiry settings
    /// </summary>
    MessageExpiry = 15,

    /// <summary>
    /// Priority messaging
    /// </summary>
    PriorityMessaging = 16,

    /// <summary>
    /// Opt-out management
    /// </summary>
    OptOutManagement = 17,

    /// <summary>
    /// Contact list management
    /// </summary>
    ContactLists = 18,

    /// <summary>
    /// Message analytics
    /// </summary>
    Analytics = 19,

    /// <summary>
    /// A/B testing
    /// </summary>
    ABTesting = 20
}

/// <summary>
/// Message type enumeration
/// </summary>
public enum MessageType
{
    /// <summary>
    /// Plain text message
    /// </summary>
    Text = 0,

    /// <summary>
    /// Unicode text message
    /// </summary>
    Unicode = 1,

    /// <summary>
    /// Flash SMS (displayed immediately)
    /// </summary>
    Flash = 2,

    /// <summary>
    /// Binary message
    /// </summary>
    Binary = 3,

    /// <summary>
    /// MMS message with attachments
    /// </summary>
    Mms = 4,

    /// <summary>
    /// Template-based message
    /// </summary>
    Template = 5
}

/// <summary>
/// Webhook event types
/// </summary>
public enum WebhookEvent
{
    /// <summary>
    /// Message sent
    /// </summary>
    MessageSent = 0,

    /// <summary>
    /// Message delivered
    /// </summary>
    MessageDelivered = 1,

    /// <summary>
    /// Message failed
    /// </summary>
    MessageFailed = 2,

    /// <summary>
    /// Message expired
    /// </summary>
    MessageExpired = 3,

    /// <summary>
    /// Message rejected
    /// </summary>
    MessageRejected = 4,

    /// <summary>
    /// Incoming message received
    /// </summary>
    IncomingMessage = 5,

    /// <summary>
    /// Opt-out received
    /// </summary>
    OptOut = 6,

    /// <summary>
    /// Opt-in received
    /// </summary>
    OptIn = 7,

    /// <summary>
    /// Account balance low
    /// </summary>
    LowBalance = 8,

    /// <summary>
    /// Rate limit exceeded
    /// </summary>
    RateLimitExceeded = 9
}

/// <summary>
/// Provider capabilities
/// </summary>
public class ProviderCapabilities
{
    /// <summary>
    /// Supported features
    /// </summary>
    public List<ProviderFeature> SupportedFeatures { get; set; } = new();

    /// <summary>
    /// Supported message types
    /// </summary>
    public List<MessageType> SupportedMessageTypes { get; set; } = new();

    /// <summary>
    /// Supported countries (ISO codes)
    /// </summary>
    public List<string> SupportedCountries { get; set; } = new();

    /// <summary>
    /// Supported character sets
    /// </summary>
    public List<string> SupportedCharsets { get; set; } = new();

    /// <summary>
    /// Maximum message length for single SMS
    /// </summary>
    public int MaxSingleSmsLength { get; set; } = 160;

    /// <summary>
    /// Maximum message length for Unicode SMS
    /// </summary>
    public int MaxUnicodeSmsLength { get; set; } = 70;

    /// <summary>
    /// Maximum message length for concatenated SMS
    /// </summary>
    public int MaxConcatenatedSmsLength { get; set; } = 1600;

    /// <summary>
    /// Maximum number of parts in concatenated SMS
    /// </summary>
    public int MaxConcatenatedParts { get; set; } = 10;

    /// <summary>
    /// Supports custom sender ID
    /// </summary>
    public bool SupportsCustomSenderId { get; set; } = true;

    /// <summary>
    /// Supports alphanumeric sender ID
    /// </summary>
    public bool SupportsAlphanumericSenderId { get; set; } = true;

    /// <summary>
    /// Maximum sender ID length
    /// </summary>
    public int MaxSenderIdLength { get; set; } = 11;

    /// <summary>
    /// Supports delivery reports
    /// </summary>
    public bool SupportsDeliveryReports { get; set; } = true;

    /// <summary>
    /// Supports scheduled sending
    /// </summary>
    public bool SupportsScheduledSending { get; set; } = true;

    /// <summary>
    /// Maximum scheduling time in advance (hours)
    /// </summary>
    public int MaxSchedulingHours { get; set; } = 24 * 30; // 30 days

    /// <summary>
    /// Supports bulk sending
    /// </summary>
    public bool SupportsBulkSending { get; set; } = true;

    /// <summary>
    /// Maximum bulk size
    /// </summary>
    public int MaxBulkSize { get; set; } = 1000;

    /// <summary>
    /// Supports two-way messaging
    /// </summary>
    public bool SupportsTwoWay { get; set; } = false;

    /// <summary>
    /// Supports MMS
    /// </summary>
    public bool SupportsMms { get; set; } = false;

    /// <summary>
    /// Maximum MMS size in bytes
    /// </summary>
    public long MaxMmsSize { get; set; } = 1024 * 1024; // 1MB

    /// <summary>
    /// Supported MMS file types
    /// </summary>
    public List<string> SupportedMmsTypes { get; set; } = new();
}

/// <summary>
/// Provider limits and quotas
/// </summary>
public class ProviderLimits
{
    /// <summary>
    /// Rate limit per second
    /// </summary>
    public int? RateLimitPerSecond { get; set; }

    /// <summary>
    /// Rate limit per minute
    /// </summary>
    public int? RateLimitPerMinute { get; set; }

    /// <summary>
    /// Rate limit per hour
    /// </summary>
    public int? RateLimitPerHour { get; set; }

    /// <summary>
    /// Rate limit per day
    /// </summary>
    public int? RateLimitPerDay { get; set; }

    /// <summary>
    /// Monthly quota
    /// </summary>
    public int? MonthlyQuota { get; set; }

    /// <summary>
    /// Concurrent connection limit
    /// </summary>
    public int? MaxConcurrentConnections { get; set; }

    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    public int RequestTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Maximum retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Retry delay in seconds
    /// </summary>
    public int RetryDelaySeconds { get; set; } = 5;

    /// <summary>
    /// Minimum balance required
    /// </summary>
    public decimal? MinimumBalance { get; set; }

    /// <summary>
    /// Low balance threshold
    /// </summary>
    public decimal? LowBalanceThreshold { get; set; }
}
