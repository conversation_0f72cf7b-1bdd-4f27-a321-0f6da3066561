namespace SmsGateway.Core.Models.Common;

/// <summary>
/// SMS message status enumeration
/// </summary>
public enum SmsStatus
{
    /// <summary>
    /// Message is pending processing
    /// </summary>
    Pending = 0,

    /// <summary>
    /// Message is queued for sending
    /// </summary>
    Queued = 1,

    /// <summary>
    /// Message has been sent to provider
    /// </summary>
    Sent = 2,

    /// <summary>
    /// Message has been delivered to recipient
    /// </summary>
    Delivered = 3,

    /// <summary>
    /// Message delivery failed
    /// </summary>
    Failed = 4,

    /// <summary>
    /// Message was rejected by provider
    /// </summary>
    Rejected = 5,

    /// <summary>
    /// Message delivery status is unknown
    /// </summary>
    Unknown = 6,

    /// <summary>
    /// Message could not be delivered
    /// </summary>
    Undelivered = 7,

    /// <summary>
    /// Message is scheduled for future delivery
    /// </summary>
    Scheduled = 8,

    /// <summary>
    /// Message was cancelled before sending
    /// </summary>
    Cancelled = 9
}

/// <summary>
/// SMS message priority enumeration
/// </summary>
public enum SmsPriority
{
    /// <summary>
    /// Low priority message
    /// </summary>
    Low = 0,

    /// <summary>
    /// Normal priority message (default)
    /// </summary>
    Normal = 1,

    /// <summary>
    /// High priority message
    /// </summary>
    High = 2,

    /// <summary>
    /// Critical priority message
    /// </summary>
    Critical = 3
}

/// <summary>
/// Queue processing status enumeration
/// </summary>
public enum QueueStatus
{
    /// <summary>
    /// Item is pending processing
    /// </summary>
    Pending = 0,

    /// <summary>
    /// Item is currently being processed
    /// </summary>
    Processing = 1,

    /// <summary>
    /// Item has been processed successfully
    /// </summary>
    Completed = 2,

    /// <summary>
    /// Item processing failed
    /// </summary>
    Failed = 3,

    /// <summary>
    /// Item was cancelled
    /// </summary>
    Cancelled = 4,

    /// <summary>
    /// Item is scheduled for future processing
    /// </summary>
    Scheduled = 5
}

/// <summary>
/// Provider configuration type enumeration
/// </summary>
public enum ProviderConfigurationType
{
    /// <summary>
    /// Basic configuration with minimal settings
    /// </summary>
    Basic = 0,

    /// <summary>
    /// API key based authentication
    /// </summary>
    ApiKey = 1,

    /// <summary>
    /// Username and password authentication
    /// </summary>
    UsernamePassword = 2,

    /// <summary>
    /// OAuth 2.0 authentication
    /// </summary>
    OAuth = 3,

    /// <summary>
    /// Custom configuration type
    /// </summary>
    Custom = 4
}

/// <summary>
/// Template variable type enumeration
/// </summary>
public enum TemplateVariableType
{
    /// <summary>
    /// String variable
    /// </summary>
    String = 0,

    /// <summary>
    /// Numeric variable
    /// </summary>
    Number = 1,

    /// <summary>
    /// Boolean variable
    /// </summary>
    Boolean = 2,

    /// <summary>
    /// Date/time variable
    /// </summary>
    DateTime = 3,

    /// <summary>
    /// Array/list variable
    /// </summary>
    Array = 4,

    /// <summary>
    /// Object variable
    /// </summary>
    Object = 5
}

/// <summary>
/// User role enumeration
/// </summary>
public enum UserRole
{
    /// <summary>
    /// Regular user with basic permissions
    /// </summary>
    User = 0,

    /// <summary>
    /// Moderator with extended permissions
    /// </summary>
    Moderator = 1,

    /// <summary>
    /// Administrator with full permissions
    /// </summary>
    Administrator = 2,

    /// <summary>
    /// Super administrator with system-level permissions
    /// </summary>
    SuperAdministrator = 3
}

/// <summary>
/// Audit action type enumeration
/// </summary>
public enum AuditActionType
{
    /// <summary>
    /// Create operation
    /// </summary>
    Create = 0,

    /// <summary>
    /// Read operation
    /// </summary>
    Read = 1,

    /// <summary>
    /// Update operation
    /// </summary>
    Update = 2,

    /// <summary>
    /// Delete operation
    /// </summary>
    Delete = 3,

    /// <summary>
    /// Login operation
    /// </summary>
    Login = 4,

    /// <summary>
    /// Logout operation
    /// </summary>
    Logout = 5,

    /// <summary>
    /// Send SMS operation
    /// </summary>
    SendSms = 6,

    /// <summary>
    /// Configuration change
    /// </summary>
    ConfigurationChange = 7,

    /// <summary>
    /// Plugin operation
    /// </summary>
    PluginOperation = 8
}

/// <summary>
/// Log level enumeration
/// </summary>
public enum LogLevel
{
    /// <summary>
    /// Trace level logging
    /// </summary>
    Trace = 0,

    /// <summary>
    /// Debug level logging
    /// </summary>
    Debug = 1,

    /// <summary>
    /// Information level logging
    /// </summary>
    Information = 2,

    /// <summary>
    /// Warning level logging
    /// </summary>
    Warning = 3,

    /// <summary>
    /// Error level logging
    /// </summary>
    Error = 4,

    /// <summary>
    /// Critical level logging
    /// </summary>
    Critical = 5
}
