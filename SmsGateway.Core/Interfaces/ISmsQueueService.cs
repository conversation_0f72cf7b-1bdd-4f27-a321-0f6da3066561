using SmsGateway.Core.Models;
using SmsGateway.Core.Models.Database;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// Interface for SMS queue management and background processing
/// </summary>
public interface ISmsQueueService
{
    /// <summary>
    /// Queue an SMS message for background processing
    /// </summary>
    /// <param name="request">SMS request</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="priority">Queue priority (lower = higher priority)</param>
    /// <param name="processAt">When to process the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Queue item ID</returns>
    Task<Guid> QueueSmsAsync(SmsRequest request, string tenantId, int priority = 100, DateTime? processAt = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Queue multiple SMS messages for batch processing
    /// </summary>
    /// <param name="requests">SMS requests</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="priority">Queue priority</param>
    /// <param name="processAt">When to process the messages</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of queue item IDs</returns>
    Task<IEnumerable<Guid>> QueueBatchSmsAsync(IEnumerable<SmsRequest> requests, string tenantId, int priority = 100, DateTime? processAt = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process a queued SMS message
    /// </summary>
    /// <param name="queueId">Queue item ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result</returns>
    Task<QueueProcessingResult> ProcessQueuedSmsAsync(Guid queueId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get queue status
    /// </summary>
    /// <param name="queueId">Queue item ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Queue status or null if not found</returns>
    Task<QueueStatusResponse?> GetQueueStatusAsync(Guid queueId, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get queue statistics for a tenant
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Queue statistics</returns>
    Task<QueueStatistics> GetQueueStatisticsAsync(string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel a queued message
    /// </summary>
    /// <param name="queueId">Queue item ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cancelled successfully</returns>
    Task<bool> CancelQueuedSmsAsync(Guid queueId, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retry a failed queued message
    /// </summary>
    /// <param name="queueId">Queue item ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if retry was scheduled successfully</returns>
    Task<bool> RetryQueuedSmsAsync(Guid queueId, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pending queue items for processing
    /// </summary>
    /// <param name="maxItems">Maximum number of items to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of pending queue items</returns>
    Task<IEnumerable<SmsQueue>> GetPendingQueueItemsAsync(int maxItems = 100, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clean up old completed/failed queue items
    /// </summary>
    /// <param name="olderThan">Remove items older than this date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of items cleaned up</returns>
    Task<int> CleanupOldQueueItemsAsync(DateTime olderThan, CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of queue processing operation
/// </summary>
public class QueueProcessingResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public SmsResponse? SmsResponse { get; set; }
    public int AttemptCount { get; set; }
    public bool ShouldRetry { get; set; }
    public DateTime? NextRetryAt { get; set; }
}

/// <summary>
/// Queue status response
/// </summary>
public class QueueStatusResponse
{
    public Guid QueueId { get; set; }
    public Guid SmsMessageId { get; set; }
    public QueueStatus Status { get; set; }
    public int Priority { get; set; }
    public int AttemptCount { get; set; }
    public int MaxAttempts { get; set; }
    public DateTime ProcessAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessingStartedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public string? LastError { get; set; }
    public string? HangfireJobId { get; set; }
}

/// <summary>
/// Queue statistics
/// </summary>
public class QueueStatistics
{
    public string TenantId { get; set; } = string.Empty;
    public int PendingCount { get; set; }
    public int ProcessingCount { get; set; }
    public int CompletedCount { get; set; }
    public int FailedCount { get; set; }
    public int CancelledCount { get; set; }
    public int ScheduledCount { get; set; }
    public int TotalCount { get; set; }
    public TimeSpan AverageProcessingTime { get; set; }
    public DateTime? OldestPendingItem { get; set; }
    public Dictionary<string, int>? StatusBreakdown { get; set; }
    public Dictionary<string, int>? PriorityBreakdown { get; set; }
}
