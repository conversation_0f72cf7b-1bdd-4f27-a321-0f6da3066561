using SmsGateway.Core.Models.Authentication;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// Interface for authentication and authorization services
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// Authenticate user with username/password
    /// </summary>
    /// <param name="request">Login request</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="userAgent">Client user agent</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Login response</returns>
    Task<LoginResponse> LoginAsync(LoginRequest request, string? ipAddress = null, string? userAgent = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Refresh access token using refresh token
    /// </summary>
    /// <param name="request">Refresh token request</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New login response</returns>
    Task<LoginResponse> RefreshTokenAsync(RefreshTokenRequest request, string? ipAddress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Logout user and invalidate session
    /// </summary>
    /// <param name="tokenId">JWT token ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if logout was successful</returns>
    Task<bool> LogoutAsync(string tokenId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Register a new user
    /// </summary>
    /// <param name="request">Registration request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User information</returns>
    Task<UserInfo> RegisterAsync(RegisterRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Change user password
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="request">Change password request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if password was changed successfully</returns>
    Task<bool> ChangePasswordAsync(Guid userId, ChangePasswordRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Request password reset
    /// </summary>
    /// <param name="request">Reset password request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if reset email was sent</returns>
    Task<bool> RequestPasswordResetAsync(ResetPasswordRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Confirm password reset with token
    /// </summary>
    /// <param name="request">Confirm password reset request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if password was reset successfully</returns>
    Task<bool> ConfirmPasswordResetAsync(ConfirmPasswordResetRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Verify email address with token
    /// </summary>
    /// <param name="token">Email verification token</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if email was verified successfully</returns>
    Task<bool> VerifyEmailAsync(string token, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user information by ID
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User information or null if not found</returns>
    Task<UserInfo?> GetUserAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user information by username
    /// </summary>
    /// <param name="username">Username</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User information or null if not found</returns>
    Task<UserInfo?> GetUserByUsernameAsync(string username, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if user has permission
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="permission">Permission to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user has permission</returns>
    Task<bool> HasPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user permissions
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of user permissions</returns>
    Task<List<string>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate JWT token
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Token validation result</returns>
    Task<TokenValidationResult> ValidateTokenAsync(string token, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for API key management
/// </summary>
public interface IApiKeyService
{
    /// <summary>
    /// Create a new API key
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="request">API key creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>API key response with the key value</returns>
    Task<ApiKeyResponse> CreateApiKeyAsync(Guid userId, CreateApiKeyRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get API keys for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of API keys (without key values)</returns>
    Task<List<ApiKeyResponse>> GetApiKeysAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Revoke an API key
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="apiKeyId">API key ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if API key was revoked</returns>
    Task<bool> RevokeApiKeyAsync(Guid userId, Guid apiKeyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate API key
    /// </summary>
    /// <param name="apiKey">API key value</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>API key validation result</returns>
    Task<ApiKeyValidationResult> ValidateApiKeyAsync(string apiKey, string? ipAddress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update API key usage statistics
    /// </summary>
    /// <param name="apiKeyId">API key ID</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateApiKeyUsageAsync(Guid apiKeyId, string? ipAddress = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Token validation result
/// </summary>
public class TokenValidationResult
{
    public bool IsValid { get; set; }
    public Guid? UserId { get; set; }
    public string? TenantId { get; set; }
    public string? TokenId { get; set; }
    public List<string> Permissions { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// API key validation result
/// </summary>
public class ApiKeyValidationResult
{
    public bool IsValid { get; set; }
    public Guid? UserId { get; set; }
    public Guid? ApiKeyId { get; set; }
    public string? TenantId { get; set; }
    public List<string> Permissions { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public int? RateLimitPerMinute { get; set; }
    public bool IsRateLimited { get; set; }
}
