using SmsGateway.Core.Models;
using SmsGateway.Core.Models.Common;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// Interface for plugin factories that create provider instances
/// </summary>
public interface IPluginFactory
{
    /// <summary>
    /// Plugin metadata
    /// </summary>
    PluginMetadata Metadata { get; }

    /// <summary>
    /// Create provider instances from this plugin
    /// </summary>
    /// <returns>Collection of provider instances</returns>
    IEnumerable<ISmsProvider> CreateProviders();

    /// <summary>
    /// Get supported configuration types for this plugin
    /// </summary>
    /// <returns>Collection of configuration types</returns>
    IEnumerable<Type> GetSupportedConfigurationTypes();

    /// <summary>
    /// Validate plugin dependencies
    /// </summary>
    /// <returns>Validation result</returns>
    ValidationResult ValidateDependencies();
}

/// <summary>
/// Base class for plugin factories
/// </summary>
public abstract class PluginFactoryBase : IPluginFactory
{
    public abstract PluginMetadata Metadata { get; }
    
    public abstract IEnumerable<ISmsProvider> CreateProviders();
    
    public virtual IEnumerable<Type> GetSupportedConfigurationTypes()
    {
        return new[] { typeof(ProviderConfiguration) };
    }
    
    public virtual ValidationResult ValidateDependencies()
    {
        return new ValidationResult { IsValid = true };
    }
}
