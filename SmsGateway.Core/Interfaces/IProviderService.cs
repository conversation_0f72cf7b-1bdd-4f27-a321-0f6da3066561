using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// Provider service interface for managing SMS providers
/// </summary>
public interface IProviderService
{
    /// <summary>
    /// Get all available SMS providers
    /// </summary>
    /// <param name="includeDisabled">Include disabled providers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of provider information</returns>
    Task<List<ProviderInfoResponse>> GetProvidersAsync(bool includeDisabled = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get specific provider information
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Provider information</returns>
    Task<ProviderInfoResponse?> GetProviderAsync(string providerName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get provider health status
    /// </summary>
    /// <param name="request">Health check request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Provider health status</returns>
    Task<List<ProviderHealthResponse>> GetProviderHealthAsync(GetProviderHealthRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get provider statistics
    /// </summary>
    /// <param name="request">Statistics request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Provider statistics</returns>
    Task<List<ProviderStatisticsResponse>> GetProviderStatisticsAsync(GetProviderStatisticsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Test provider connectivity and configuration
    /// </summary>
    /// <param name="request">Provider test request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Test result</returns>
    Task<ProviderTestResponse> TestProviderAsync(TestProviderRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update provider configuration
    /// </summary>
    /// <param name="request">Configuration update request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated provider information</returns>
    Task<ProviderInfoResponse?> UpdateProviderConfigurationAsync(UpdateProviderConfigurationRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pricing information for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="countryCode">Optional country code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Pricing information</returns>
    Task<PricingInfoResponse?> GetProviderPricingAsync(string providerName, string? countryCode = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get account balance for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Account balance information</returns>
    Task<AccountBalanceResponse?> GetProviderBalanceAsync(string providerName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get rate limit status for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Rate limit information</returns>
    Task<RateLimitResponse?> GetProviderRateLimitAsync(string providerName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get loaded plugins
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of loaded plugins</returns>
    Task<List<PluginInfoResponse>> GetLoadedPluginsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Load a plugin
    /// </summary>
    /// <param name="request">Plugin load request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Plugin load result</returns>
    Task<PluginLoadResponse> LoadPluginAsync(LoadPluginRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Unload a plugin
    /// </summary>
    /// <param name="request">Plugin unload request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Plugin unload result</returns>
    Task<PluginLoadResponse> UnloadPluginAsync(UnloadPluginRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Configure webhook for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="request">Webhook configuration request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Webhook configuration result</returns>
    Task<WebhookConfigResponse> ConfigureWebhookAsync(string providerName, object request, CancellationToken cancellationToken = default);
}
