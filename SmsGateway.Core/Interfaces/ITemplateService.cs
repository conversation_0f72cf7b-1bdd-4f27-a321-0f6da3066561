using SmsGateway.Core.Models.Database;
using SmsGateway.Core.Models.Templates;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// Interface for SMS template management and processing
/// </summary>
public interface ITemplateService
{
    /// <summary>
    /// Create a new SMS template
    /// </summary>
    /// <param name="request">Template creation request</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="createdBy">User who created the template</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created template response</returns>
    Task<TemplateResponse> CreateTemplateAsync(TemplateRequest request, string tenantId, string? createdBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update an existing SMS template
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="request">Template update request</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="updatedBy">User who updated the template</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated template response</returns>
    Task<TemplateResponse?> UpdateTemplateAsync(Guid templateId, TemplateRequest request, string tenantId, string? updatedBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete an SMS template
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteTemplateAsync(Guid templateId, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get an SMS template by ID
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template response or null if not found</returns>
    Task<TemplateResponse?> GetTemplateAsync(Guid templateId, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get an SMS template by name
    /// </summary>
    /// <param name="templateName">Template name</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template response or null if not found</returns>
    Task<TemplateResponse?> GetTemplateByNameAsync(string templateName, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all SMS templates for a tenant
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="category">Optional category filter</param>
    /// <param name="isActive">Optional active status filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of template responses</returns>
    Task<IEnumerable<TemplateResponse>> GetTemplatesAsync(string tenantId, string? category = null, bool? isActive = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process a message using a template
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="variables">Variables for template processing</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processed message content</returns>
    Task<string> ProcessTemplateAsync(Guid templateId, Dictionary<string, object> variables, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process a message using a template by name
    /// </summary>
    /// <param name="templateName">Template name</param>
    /// <param name="variables">Variables for template processing</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processed message content</returns>
    Task<string> ProcessTemplateByNameAsync(string templateName, Dictionary<string, object> variables, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process template content directly (without saving to database)
    /// </summary>
    /// <param name="templateContent">Template content with Liquid syntax</param>
    /// <param name="variables">Variables for template processing</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processed message content</returns>
    Task<string> ProcessTemplateContentAsync(string templateContent, Dictionary<string, object> variables, CancellationToken cancellationToken = default);

    /// <summary>
    /// Test template rendering
    /// </summary>
    /// <param name="request">Template test request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template test response</returns>
    Task<TemplateTestResponse> TestTemplateAsync(TemplateTestRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate template syntax
    /// </summary>
    /// <param name="templateContent">Template content to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<ValidationResult> ValidateTemplateAsync(string templateContent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Extract variables from template content
    /// </summary>
    /// <param name="templateContent">Template content</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of variable names found in template</returns>
    Task<List<string>> ExtractVariablesAsync(string templateContent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get template usage statistics
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template usage statistics</returns>
    Task<TemplateUsageStatistics?> GetTemplateUsageAsync(Guid templateId, string tenantId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Template usage statistics
/// </summary>
public class TemplateUsageStatistics
{
    public Guid TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public int TotalUsage { get; set; }
    public int SuccessfulMessages { get; set; }
    public int FailedMessages { get; set; }
    public decimal SuccessRate => TotalUsage > 0 ? (decimal)SuccessfulMessages / TotalUsage * 100 : 0;
    public DateTime? LastUsed { get; set; }
    public Dictionary<string, int>? UsageByProvider { get; set; }
    public Dictionary<string, int>? UsageByMonth { get; set; }
}
