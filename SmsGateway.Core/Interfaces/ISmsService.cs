using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using SmsGateway.Core.Models.Common;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// SMS service interface for high-level SMS operations
/// </summary>
public interface ISmsService
{
    /// <summary>
    /// Send a single SMS message
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response with delivery details</returns>
    Task<SmsResponse> SendSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send multiple SMS messages in a batch
    /// </summary>
    /// <param name="request">Bulk SMS request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk SMS response</returns>
    Task<BulkSmsResponse> SendBulkSmsAsync(SendBulkSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedule an SMS message for future delivery
    /// </summary>
    /// <param name="request">SMS request with scheduled time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response</returns>
    Task<SmsResponse> ScheduleSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel a scheduled SMS message
    /// </summary>
    /// <param name="messageId">Message ID to cancel</param>
    /// <param name="provider">Provider that scheduled the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cancelled successfully</returns>
    Task<bool> CancelScheduledSmsAsync(string messageId, string provider, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get the status of a previously sent message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="provider">Provider that sent the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current message status</returns>
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId, string provider, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get detailed message information
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="provider">Provider that sent the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detailed message information</returns>
    Task<MessageDetailsResponse> GetMessageDetailsAsync(string messageId, string provider, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get delivery report for a message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="provider">Provider that sent the message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery report</returns>
    Task<DeliveryReportResponse> GetDeliveryReportAsync(string messageId, string provider, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get status of multiple messages
    /// </summary>
    /// <param name="messageIds">List of message IDs</param>
    /// <param name="provider">Provider that sent the messages</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Status responses for each message</returns>
    Task<List<MessageStatusResponse>> GetBulkMessageStatusAsync(List<string> messageIds, string provider, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get estimated cost for sending an SMS
    /// </summary>
    /// <param name="request">Cost estimation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Estimated cost and currency</returns>
    Task<CostEstimateResponse> GetEstimatedCostAsync(GetCostEstimateRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get SMS message history
    /// </summary>
    /// <param name="request">History query parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS message history</returns>
    Task<SmsHistoryResponse> GetSmsHistoryAsync(GetSmsHistoryRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send SMS with template
    /// </summary>
    /// <param name="request">Template SMS request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response</returns>
    Task<SmsResponse> SendTemplateSmsAsync(SendTemplateSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate phone number
    /// </summary>
    /// <param name="phoneNumber">Phone number to validate</param>
    /// <param name="countryCode">Optional country code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Phone validation result</returns>
    Task<PhoneValidationResponse> ValidatePhoneNumberAsync(string phoneNumber, string? countryCode = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate message content
    /// </summary>
    /// <param name="message">Message content</param>
    /// <param name="messageType">Type of message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message validation result</returns>
    Task<MessageValidationResponse> ValidateMessageAsync(string message, MessageType messageType = MessageType.Text, CancellationToken cancellationToken = default);
}
