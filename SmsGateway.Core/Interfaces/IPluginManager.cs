﻿﻿﻿using SmsGateway.Core.Models;
using SmsGateway.Core.Models.Common;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// Interface for managing SMS provider plugins with runtime loading/unloading
/// </summary>
public interface IPluginManager
{
    /// <summary>
    /// Load all available plugins from the plugins directory
    /// </summary>
    Task LoadPluginsAsync();

    /// <summary>
    /// Load a specific plugin from file path
    /// </summary>
    /// <param name="pluginPath">Path to the plugin assembly</param>
    /// <returns>Load result with plugin information</returns>
    Task<PluginLoadResult> LoadPluginAsync(string pluginPath);

    /// <summary>
    /// Unload a specific plugin
    /// </summary>
    /// <param name="pluginName">Name of the plugin to unload</param>
    /// <returns>True if successfully unloaded</returns>
    Task<bool> UnloadPluginAsync(string pluginName);

    /// <summary>
    /// Reload a plugin (unload and load again)
    /// </summary>
    /// <param name="pluginName">Name of the plugin to reload</param>
    /// <returns>Reload result</returns>
    Task<PluginLoadResult> ReloadPluginAsync(string pluginName);

    /// <summary>
    /// Register a provider plugin
    /// </summary>
    /// <param name="provider">Provider instance to register</param>
    /// <param name="configuration">Provider configuration</param>
    Task RegisterProviderAsync(ISmsProvider provider, ProviderConfiguration configuration);

    /// <summary>
    /// Unregister a provider plugin
    /// </summary>
    /// <param name="providerName">Name of the provider to unregister</param>
    Task UnregisterProviderAsync(string providerName);

    /// <summary>
    /// Get a specific provider by name
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Provider instance or null if not found</returns>
    ISmsProvider? GetProvider(string providerName);

    /// <summary>
    /// Get all registered providers
    /// </summary>
    /// <returns>Collection of all registered providers</returns>
    IEnumerable<ISmsProvider> GetAllProviders();

    /// <summary>
    /// Get all enabled providers ordered by priority
    /// </summary>
    /// <returns>Collection of enabled providers in priority order</returns>
    IEnumerable<ISmsProvider> GetEnabledProviders();

    /// <summary>
    /// Get the best provider for a specific request
    /// </summary>
    /// <param name="request">SMS request to find provider for</param>
    /// <returns>Best matching provider or null if none available</returns>
    ISmsProvider? GetBestProvider(SmsRequest request);

    /// <summary>
    /// Get provider configuration
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Provider configuration or null if not found</returns>
    ProviderConfiguration? GetProviderConfiguration(string providerName);

    /// <summary>
    /// Update provider configuration
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <param name="configuration">New configuration</param>
    Task UpdateProviderConfigurationAsync(string providerName, ProviderConfiguration configuration);

    /// <summary>
    /// Check if a provider is registered and enabled
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>True if provider is registered and enabled</returns>
    bool IsProviderEnabled(string providerName);

    /// <summary>
    /// Get provider statistics
    /// </summary>
    /// <param name="providerName">Name of the provider</param>
    /// <returns>Provider statistics or null if not found</returns>
    ProviderStatistics? GetProviderStatistics(string providerName);

    /// <summary>
    /// Get all loaded plugins
    /// </summary>
    /// <returns>Collection of loaded plugin information</returns>
    IEnumerable<LoadedPluginInfo> GetLoadedPlugins();

    /// <summary>
    /// Get information about a specific plugin
    /// </summary>
    /// <param name="pluginName">Name of the plugin</param>
    /// <returns>Plugin information or null if not found</returns>
    LoadedPluginInfo? GetPluginInfo(string pluginName);

    /// <summary>
    /// Scan for new plugins in the plugins directory
    /// </summary>
    /// <returns>Collection of discovered plugin files</returns>
    Task<IEnumerable<string>> ScanForPluginsAsync();

    /// <summary>
    /// Update statistics for a provider
    /// </summary>
    /// <param name="providerName">Provider name</param>
    /// <param name="success">Whether the operation was successful</param>
    /// <param name="responseTime">Response time</param>
    /// <param name="error">Error message if any</param>
    void UpdateStatistics(string providerName, bool success, TimeSpan responseTime, string? error = null);
}

/// <summary>
/// Result of plugin loading operation
/// </summary>
public class PluginLoadResult
{
    public bool Success { get; set; }
    public string? PluginName { get; set; }
    public List<string> LoadedProviders { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public PluginMetadata? Metadata { get; set; }
}



/// <summary>
/// Information about a loaded plugin
/// </summary>
public class LoadedPluginInfo
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string AssemblyPath { get; set; } = string.Empty;
    public DateTime LoadedAt { get; set; }
    public bool IsLoaded { get; set; }
    public List<string> ProviderNames { get; set; } = new();
    public PluginMetadata? Metadata { get; set; }
    public object? AssemblyLoadContext { get; set; }
    public List<string> LoadErrors { get; set; } = new();
}
